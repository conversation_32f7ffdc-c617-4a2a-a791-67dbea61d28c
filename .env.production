# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'


# Basic interface address SPA
VITE_GLOB_API_URL = /api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL = /upload

# OSS upload address for production (use full domain in production)
VITE_GLOB_OSS_URL = https://ossxjp.xyxpos.com

# Interface prefix
VITE_GLOB_API_URL_PREFIX =
