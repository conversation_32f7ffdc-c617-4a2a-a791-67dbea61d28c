import { find } from 'lodash-es';
import { FILE_TYPE_MAP, type Accept } from '../constants/fileTypes';
import type { FileModel } from '../types/UploadFile';

/**
 * 解析文件URL获取文件信息
 * Parse file URL to get file information
 * @param url 文件URL
 * @returns 解析后的文件信息
 */
export function parseFileUrl(url: string): FileModel {
  try {
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    let fileName = '';

    // 查找查询参数里第一个带 '.' 的值作为文件名
    // Find the first value with '.' in query parameters as filename
    for (const [_, value] of searchParams) {
      if (value.includes('.')) {
        fileName = value;
        break;
      }
    }

    // 如果查询参数里没有符合的文件名，则从路径获取
    // If no suitable filename in query parameters, get from path
    if (!fileName) {
      const pathSegments = urlObj.pathname.split('/').filter(Boolean);
      fileName = pathSegments.pop() || '';
    }

    // 获取 bucketName（倒数第二个路径段）
    // Get bucketName (second to last path segment)
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    const bucketName = pathSegments.length > 1 ? pathSegments[pathSegments.length - 2] : '';

    // 解析文件后缀
    // Parse file suffix
    const suffix = fileName.includes('.') ? fileName.split('.').pop()?.toUpperCase() : '';

    // 查找对应的图标和预览工具
    // Find corresponding icon and preview tool
    const fileTypeConfig = suffix ? find(FILE_TYPE_MAP, (item) => item.name === suffix) : undefined;
    const icon = fileTypeConfig?.icon || 'vscode-icons:default-file';
    const previewTool = fileTypeConfig?.previewTool;

    return {
      url,
      fileName,
      bucketName,
      suffix: suffix || '',
      icon,
      previewTool,
    };
  } catch (error) {
    console.error('Failed to parse file URL:', error);
    return {
      url: '',
      fileName: '文件解析错误', // TODO: 需要国际化 - File parse error
      bucketName: '',
      suffix: '',
      icon: 'vscode-icons:default-file',
    };
  }
}

/**
 * 验证文件类型和名称
 * Validate file type and name
 * @param file 文件对象
 * @param acceptList 允许的文件类型列表
 * @returns 验证结果和错误信息
 */
export function validateFile(file: File, acceptList: Accept[]): { valid: boolean; error?: string } {
  const { name, type } = file;

  // 检查文件名是否包含逗号
  // Check if filename contains comma
  if (/,/.test(name)) {
    return {
      valid: false,
      error: '文件名不能包含逗号', // TODO: 需要国际化 - Filename cannot contain commas
    };
  }

  // 检查文件类型是否支持
  // Check if file type is supported
  if (!acceptList.includes(type as Accept)) {
    return {
      valid: false,
      error: 'UNSUPPORTED_FILE_TYPE', // 使用标识符，在组件中处理国际化
    };
  }

  return { valid: true };
}

/**
 * 格式化文件大小
 * Format file size
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查是否为图片文件
 * Check if file is an image
 * @param file 文件对象
 * @returns 是否为图片文件
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * 获取文件扩展名
 * Get file extension
 * @param filename 文件名
 * @returns 文件扩展名（大写）
 */
export function getFileExtension(filename: string): string {
  return filename.includes('.') ? filename.split('.').pop()?.toUpperCase() || '' : '';
}

/**
 * 根据文件类型获取图标
 * Get icon by file type
 * @param fileType 文件类型
 * @returns 图标名称
 */
export function getFileIcon(fileType: string): string {
  const config = find(FILE_TYPE_MAP, (item) => item.name === fileType.toUpperCase());
  return config?.icon || 'vscode-icons:default-file';
}

/**
 * 检查文件是否支持预览
 * Check if file supports preview
 * @param fileType 文件类型
 * @returns 是否支持预览和预览工具类型
 */
export function getPreviewInfo(fileType: string): { supported: boolean; tool?: string } {
  const config = find(FILE_TYPE_MAP, (item) => item.name === fileType.toUpperCase());
  return {
    supported: !!config?.previewTool,
    tool: config?.previewTool,
  };
}
