/**
 * 文件上传支持的类型配置
 * File upload supported type configuration
 */

export type Accept =
  | 'image/jpeg'
  | 'image/png'
  | 'image/jpg'
  | 'image/gif'
  | 'image/vnd.microsoft.icon'
  | 'image/x-icon'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  | 'application/vnd.ms-powerpoint'
  | 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  | 'text/plain'
  | 'application/zip'
  | 'application/vnd.rar'
  | 'application/vnd.android.package-archive';

export type FileType =
  | 'JPEG'
  | 'PNG'
  | 'JPG'
  | 'GIF'
  | 'ICO'
  | 'PDF'
  | 'DOC'
  | 'DOCX'
  | 'XLS'
  | 'XLSX'
  | 'PPT'
  | 'PPTX'
  | 'TXT'
  | 'ZIP'
  | 'RAR'
  | 'APK';

export interface AcceptMap {
  name: FileType;
  value: Accept[];
  icon: string;
  /** 预览工具类型 */
  previewTool?: string;
}

/**
 * 支持的文件类型映射配置
 * Supported file type mapping configuration
 */
export const FILE_TYPE_MAP: AcceptMap[] = [
  {
    name: 'JPEG',
    value: ['image/jpeg'],
    icon: 'vscode-icons:file-type-image',
    previewTool: 'image',
  },
  {
    name: 'PNG',
    value: ['image/png'],
    icon: 'vscode-icons:file-type-image',
    previewTool: 'image',
  },
  {
    name: 'JPG',
    value: ['image/jpg'],
    icon: 'vscode-icons:file-type-image',
    previewTool: 'image',
  },
  {
    name: 'GIF',
    value: ['image/gif'],
    icon: 'vscode-icons:file-type-image',
    previewTool: 'image',
  },
  {
    name: 'ICO',
    value: ['image/vnd.microsoft.icon', 'image/x-icon'],
    icon: 'vscode-icons:folder-type-favicon',
    previewTool: 'image',
  },
  {
    name: 'PDF',
    value: ['application/pdf'],
    icon: 'vscode-icons:file-type-pdf2',
  },
  {
    name: 'DOC',
    value: ['application/msword'],
    icon: 'vscode-icons:file-type-word',
  },
  {
    name: 'DOCX',
    value: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    icon: 'vscode-icons:file-type-word2',
  },
  {
    name: 'XLS',
    value: ['application/vnd.ms-excel'],
    icon: 'vscode-icons:file-type-excel',
  },
  {
    name: 'XLSX',
    value: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    icon: 'vscode-icons:file-type-excel2',
  },
  {
    name: 'PPT',
    value: ['application/vnd.ms-powerpoint'],
    icon: 'vscode-icons:file-type-powerpoint',
  },
  {
    name: 'PPTX',
    value: ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
    icon: 'vscode-icons:file-type-powerpoint2',
  },
  {
    name: 'TXT',
    value: ['text/plain'],
    icon: 'vscode-icons:file-type-text',
  },
  {
    name: 'ZIP',
    value: ['application/zip'],
    icon: 'vscode-icons:file-type-zip',
  },
  {
    name: 'RAR',
    value: ['application/vnd.rar'],
    icon: 'vscode-icons:file-type-zip',
  },
  {
    name: 'APK',
    value: ['application/vnd.android.package-archive'],
    icon: 'devicon:androidstudio',
  },
];

/**
 * 默认支持的文件类型
 * Default supported file types
 */
export const DEFAULT_FILE_TYPES: FileType[] = [
  'PDF',
  'DOC',
  'DOCX',
  'XLS',
  'XLSX',
  'PPT',
  'PPTX',
  'TXT',
  'JPG',
  'JPEG',
  'PNG',
  'GIF',
  'ZIP',
  'RAR',
  'APK',
];

/**
 * 图片文件类型
 * Image file types
 */
export const IMAGE_FILE_TYPES: Accept[] = [
  'image/jpeg',
  'image/png',
  'image/jpg',
  'image/gif',
  'image/vnd.microsoft.icon',
  'image/x-icon',
];

/**
 * 文件大小限制常量 (2MB)
 * File size limit constant (2MB)
 */
export const IMAGE_COMPRESS_SIZE_LIMIT = 2097152;
