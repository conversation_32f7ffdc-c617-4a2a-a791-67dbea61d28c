# UploadFile 组件

一个功能强大、美观的文件上传组件，支持多种文件类型、拖拽上传、预览等功能。

## 特性

- ✨ 支持多种文件类型（PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、图片、压缩包等）
- 🎯 支持拖拽上传
- 📱 响应式设计，移动端友好
- 🌍 完整的国际化支持
- 🎨 现代化的UI设计
- 📸 图片预览功能
- 📊 上传进度显示
- 🗂️ 文件大小显示
- 🔄 图片自动压缩（超过2MB）
- 🎭 支持预览模式和编辑模式
- 📝 多种数据格式支持（字符串、数组、对象）

## 基本用法

```vue
<template>
  <div>
    <!-- 基本用法 -->
    <UploadFile v-model:value="fileUrl" />
    
    <!-- 多文件上传 -->
    <UploadFile 
      v-model:value="fileUrls" 
      :multiple="true"
      :count="5"
      value-type="array"
    />
    
    <!-- 预览模式 -->
    <UploadFile 
      v-model:value="fileUrl" 
      :preview="true"
    />
    
    <!-- 限制文件类型 -->
    <UploadFile 
      v-model:value="fileUrl"
      :file-type="['PDF', 'DOC', 'DOCX']"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadFile from '@/components/Form/src/extend/UploadFile.vue';

const fileUrl = ref('');
const fileUrls = ref<string[]>([]);
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 组件值 | `string \| string[] \| FileResource[]` | - |
| count | 最大文件数量，0表示不限制 | `number` | `0` |
| disabled | 是否禁用 | `boolean` | `false` |
| fileType | 支持的文件类型 | `FileType[]` | `所有支持的类型` |
| preview | 是否为预览模式 | `boolean` | `false` |
| valueType | 数据格式类型 | `'string' \| 'array' \| 'object'` | `'string'` |
| multiple | 是否支持多选 | `boolean` | `false` |
| maxSize | 文件大小限制（MB） | `number` | `10` |

### FileType 支持的文件类型

```typescript
type FileType = 
  | 'JPEG' | 'PNG' | 'JPG' | 'GIF' | 'ICO'  // 图片类型
  | 'PDF'                                    // PDF文档
  | 'DOC' | 'DOCX'                          // Word文档
  | 'XLS' | 'XLSX'                          // Excel表格
  | 'PPT' | 'PPTX'                          // PowerPoint演示文稿
  | 'TXT'                                    // 文本文件
  | 'ZIP' | 'RAR'                           // 压缩文件
  | 'APK';                                   // Android安装包
```

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 文件列表变化时触发 | `(value: ValueState) => void` |
| update:value | 值更新时触发 | `(value: ValueState) => void` |

### ValueType 说明

- `'string'`: 逗号分隔的字符串格式，如 `"url1,url2,url3"`
- `'array'`: 字符串数组格式，如 `["url1", "url2", "url3"]`
- `'object'`: 对象数组格式，如 `[{url: "url1"}, {url: "url2"}]`

## 样式定制

组件使用 Less 变量，可以通过修改主题变量来定制样式：

```less
// 主要颜色
@primary-color: #1890ff;
@success-color: #52c41a;
@error-color: #ff4d4f;

// 边框和背景
@border-color-base: #d9d9d9;
@component-background: #fff;

// 文本颜色
@text-color: rgba(0, 0, 0, 0.85);
@text-color-secondary: rgba(0, 0, 0, 0.45);
```

## 国际化

组件支持国际化，需要在项目中配置相应的语言包：

```json
{
  "component": {
    "upload": {
      "upload": "上传",
      "uploading": "上传中",
      "uploadSuccess": "上传成功",
      "uploadError": "上传失败",
      "choose": "选择文件",
      "preview": "预览",
      "download": "下载",
      "del": "删除",
      "accept": "支持{0}格式",
      "acceptUpload": "只能上传{0}格式文件",
      "maxSizeMultiple": "只能上传不超过{0}MB的文件!"
    }
  }
}
```

## 注意事项

1. 图片文件超过2MB会自动压缩
2. 文件名不能包含逗号
3. 拖拽上传仅在非预览模式下可用
4. 预览功能目前仅支持图片文件
5. 组件依赖项目的上传API接口

## 更新日志

### v2.0.0 (重构版本)

- ✨ 全新的UI设计，更加现代化
- 🎯 新增拖拽上传功能
- 📱 优化响应式设计
- 🌍 完善国际化支持
- 🎨 改进动画效果
- 📊 优化上传进度显示
- 🗂️ 新增文件大小显示
- 🔧 代码结构重构，提高可维护性
- 📝 完善类型定义
- 🐛 修复已知问题
