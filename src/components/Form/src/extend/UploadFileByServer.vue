<template>
  <div :class="{ 'UploadFile-disabled': disabled }" class="UploadFile-box">
    <Button v-if="!preview" @click="method.handeUpload" :disabled="disabled">
      <template #icon>
        <CloudUploadOutlined v-show="uploadStatus === 'normal'" />
      </template>
      <span v-show="uploadStatus === 'normal'">点击上传</span>
      <span v-show="uploadStatus === 'uploading'" class="UploadFile-progress">
        <Progress :percent="progress" status="active" />
      </span>
    </Button>

    <div class="UploadFile-list">
      <div class="UploadFile-border UploadFile-item" v-for="(item, index) in fileList" :key="index">
        <div>
          <Icon v-if="item.icon" :icon="item.icon" />
          <Icon v-else icon="vscode-icons:default-file" />
        </div>
        <div class="UploadFile-item-content"> {{ item.fileName }}</div>

        <template v-if="preview">
          <div v-if="item.previewTool" class="UploadFile-item-eye">
            <EyeOutlined @click="method.handleEye(item)" />
          </div>
          <div v-if="item.url" class="UploadFile-item-download">
            <DownloadOutlined @click.stop="method.handleDown(item.url)" />
          </div>
        </template>
        <div v-else class="UploadFile-item-delete">
          <DeleteOutlined @click="method.handleDelete(index)" />
        </div>
      </div>
    </div>

    <input
      ref="fileInput"
      type="file"
      @change="onFileChange"
      class="UploadFile-input"
      :accept="acceptJoin"
      multiple
    />
    <Image
      :preview="{
        visible: visible,
        onVisibleChange: (v) => (visible = v),
      }"
      :style="{ display: 'none' }"
      :src="previewUrl"
      v-if="previewUrl"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref, unref, toRefs, computed } from 'vue';
  import {
    CloudUploadOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EyeOutlined,
  } from '@ant-design/icons-vue';
  import { Progress, message, Button, Image } from 'ant-design-vue';
  import { apiUploadFile } from '@/api/admin/file';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { FileModel, ResModel, FileResource } from './types/UploadFile';
  import { sleep } from '@/utils/other';
  import { PhotoCompress } from '@/utils/photoCompress';
  import { filter, map, flatten, get, find, size } from 'lodash-es';
  import Icon from '@/components/Icon/Icon.vue';
  import { downloadByUrl } from '@/utils/file/download';

  defineOptions({ name: 'UploadFile' });

  type ValueState = string | string[] | FileResource[];

  type Accept =
    | 'image/jpeg'
    | 'image/png'
    | 'image/jpg'
    | 'image/gif'
    | 'image/vnd.microsoft.icon'
    | 'image/x-icon'
    | 'application/pdf'
    | 'application/msword'
    | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    | 'application/vnd.ms-excel'
    | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    | 'application/vnd.ms-powerpoint'
    | 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    | 'text/plain'
    | 'application/zip'
    | 'application/vnd.rar';
  type FileType =
    | 'JPEG'
    | 'PNG'
    | 'JPG'
    | 'GIF'
    | 'ICO'
    | 'PDF'
    | 'DOC'
    | 'DOCX'
    | 'XLS'
    | 'XLSX'
    | 'PPT'
    | 'PPTX'
    | 'TXT'
    | 'ZIP'
    | 'RAR';
  interface AcceptMap {
    name: FileType;
    value: Accept[];
    icon: string;
    // 预览工具
    prTool?: string;
  }
  interface Props {
    value?: ValueState;
    count?: number;
    disabled?: boolean;
    fileType?: FileType[];
    // 预览模式
    preview?: boolean;
    /** 数据格式类型：'string' 表示逗号分隔的字符串，'array' 表示字符串数组，'object' 表示对象数组 */
    valueType?: 'string' | 'array' | 'object';
  }
  const props = withDefaults(defineProps<Props>(), {
    value: undefined,
    count: 0,
    disabled: false,
    fileType: () => [
      'PDF',
      'DOC',
      'DOCX',
      'XLS',
      'XLSX',
      'PPT',
      'PPTX',
      'TXT',
      'JPG',
      'JPEG',
      'PNG',
      'GIF',
      'ZIP',
      'RAR',
    ],
    preview: false,
    valueType: 'string',
  });
  // 允许上传类型
  const acceptMap: AcceptMap[] = [
    {
      name: 'JPEG',
      value: ['image/jpeg'],
      icon: 'vscode-icons:file-type-image',
      // 预览工具
      prTool: 'image',
    },
    {
      name: 'PNG',
      value: ['image/png'],
      icon: 'vscode-icons:file-type-image',
      prTool: 'image',
    },
    {
      name: 'JPG',
      value: ['image/jpg'],
      icon: 'vscode-icons:file-type-image',
      prTool: 'image',
    },
    {
      name: 'GIF',
      value: ['image/gif'],
      icon: 'vscode-icons:file-type-image',
      prTool: 'image',
    },
    {
      name: 'ICO',
      value: ['image/vnd.microsoft.icon', 'image/x-icon'],
      icon: 'vscode-icons:folder-type-favicon',
      prTool: 'image',
    },
    {
      name: 'PDF',
      value: ['application/pdf'],
      icon: 'vscode-icons:file-type-pdf2',
    },
    {
      name: 'DOC',
      value: ['application/msword'],
      icon: 'vscode-icons:file-type-word',
    },
    {
      name: 'DOCX',
      value: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      icon: 'vscode-icons:file-type-word2',
    },
    {
      name: 'XLS',
      value: ['application/vnd.ms-excel'],
      icon: 'vscode-icons:file-type-excel',
    },
    {
      name: 'XLSX',
      value: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
      icon: 'vscode-icons:file-type-excel2',
    },
    {
      name: 'PPT',
      value: ['application/vnd.ms-powerpoint'],
      icon: 'vscode-icons:file-type-powerpoint',
    },
    {
      name: 'PPTX',
      value: ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
      icon: 'vscode-icons:file-type-powerpoint2',
    },
    {
      name: 'TXT',
      value: ['text/plain'],
      icon: 'vscode-icons:file-type-text',
    },
    {
      name: 'ZIP',
      value: ['application/zip'],
      icon: 'vscode-icons:file-type-zip',
    },
    {
      name: 'RAR',
      value: ['application/vnd.rar'],
      icon: 'vscode-icons:file-type-zip',
    },
  ];
  defineEmits(['change', 'update:value']);
  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', 'change');
  const previewUrl = ref('');
  const visible = ref(false);
  const acceptList = computed(() => {
    let result = map(
      filter(acceptMap, (item) => props.fileType.includes(item.name)),
      'value',
    );
    return flatten(result);
  });
  const acceptJoin = computed(() => {
    if (acceptList.value?.length > 0) {
      return acceptList.value.join(',');
    } else {
      return '';
    }
  });
  const showAccept = computed(() => {
    if (props.fileType?.length > 0) {
      return props.fileType.join('、');
    } else {
      return '';
    }
  });

  const fileInput = ref<any>();
  const progress = ref(0);
  const uploadStatus = ref('normal'); // normal | uploading | success | error

  const { value, disabled } = toRefs(props);

  // 图片压缩实例
  const photoCompress = new PhotoCompress(2, 0.8);

  const fileList = computed({
    get(): FileModel[] {
      let _modelValue = unref(value);
      if (!_modelValue) return [];

      let urlList: string[] = [];

      // 根据 valueType 处理不同的数据格式
      if (props.valueType === 'string') {
        if (typeof _modelValue === 'string') {
          urlList = _modelValue.split(',').filter(Boolean);
        }
      } else if (props.valueType === 'array') {
        if (Array.isArray(_modelValue)) {
          urlList = _modelValue as string[];
        }
      } else if (props.valueType === 'object') {
        if (Array.isArray(_modelValue)) {
          urlList = (_modelValue as FileResource[]).map((item) => item.url);
        }
      }

      return urlList.map((item: string) => {
        // **尝试解析 URL**
        let urlObj: URL;
        try {
          urlObj = new URL(item);
        } catch (error) {
          return {
            url: '',
            fileName: '文件解析错误',
            bucketName: '',
            suffix: '',
            icon: '',
          };
        }

        const searchParams = urlObj.searchParams;
        let fileName = '';

        // **查找查询参数里第一个带 '.' 的值**
        for (const [_, value] of searchParams) {
          if (value.includes('.')) {
            fileName = value;
            break;
          }
        }

        // **如果查询参数里没有符合的文件名，则从路径获取**
        if (!fileName) {
          const pathSegments = urlObj.pathname.split('/').filter(Boolean);
          fileName = pathSegments.pop() || '';
        }

        // **获取 bucketName（倒数第二个路径段）**
        const pathSegments = urlObj.pathname.split('/').filter(Boolean);
        const bucketName = pathSegments.length > 1 ? pathSegments[pathSegments.length - 2] : '';

        // **解析后缀**
        const suffix = fileName.includes('.') ? fileName.split('.').pop()?.toUpperCase() : '';

        // **查找 icon**
        const accept = suffix ? find(acceptMap, (i) => i.name.includes(suffix as any)) : undefined;
        const icon = accept?.icon;
        const prTool = accept?.prTool;

        return {
          url: item,
          fileName,
          bucketName,
          suffix,
          icon,
          prTool,
        };
      });
    },
    set(val: string[]) {
      // 根据 valueType 设置不同格式的值
      if (props.valueType === 'string') {
        state.value = val.join(',');
      } else if (props.valueType === 'array') {
        state.value = val;
      } else if (props.valueType === 'object') {
        state.value = val.map((url) => ({ url }));
      }
    },
  });
  // 文件名验证方法
  function fileValidator(file: File) {
    const { name, type } = file;
    if (/,/.test(name)) {
      message.warn('文件名不能包含逗号');
      return false;
    }
    console.log(type);
    if (!acceptList.value.includes(type as Accept)) {
      message.warn(`不支持的文件类型,仅支持 ${showAccept.value} 类型`);
      return false;
    }
    return true;
  }

  function onFileChange(e: Event) {
    const target = e.target as HTMLInputElement;
    const files = target.files;
    if (files && files.length > 0) {
      let file = files[0];
      if (!fileValidator(file)) return false;

      // 如果是图片文件且大于2MB，进行压缩
      const isImage = ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type);
      if (isImage && file.size > 2097152) {
        photoCompress.compress(file, (result: File | null) => {
          if (result) {
            uploadFile(result);
          }
        });
      } else {
        uploadFile(file);
      }
    } else {
      console.warn('没有文件');
    }
  }

  async function uploadFile(file: File) {
    console.log('大小', file.size);
    try {
      progress.value = 0;
      uploadStatus.value = 'uploading';
      let res = await apiUploadFile(
        {
          file,
        },
        {
          onUploadProgress: (progressEvent: any) => {
            if (progressEvent.total) {
              progress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
            } else {
              progress.value = 0;
            }
          },
        },
      );
      console.log('上传成功', res);
      const { data, msg, code } = res as ResModel;
      if (code !== 0) {
        message.error(msg);
        return;
      }
      progress.value = 100;
      // 更新URL构建逻辑，参考UploadImage
      const url = `${get(data, 'endpoint', '')}/${get(data, 'bucketName', '')}/${get(data, 'fileName', '')}`;
      const urlList = fileList.value.map((item) => item.url);

      if (size(urlList) >= unref(props.count) && props.count !== 0) {
        urlList.shift();
      }
      urlList.push(url);
      await sleep(500);
      fileList.value = urlList;
    } catch (error) {
      message.error('上传失败');
    } finally {
      progress.value = 0;
      fileInput.value.value = '';
      uploadStatus.value = 'normal';
    }
  }

  const method = {
    // 上传
    handeUpload() {
      fileInput.value?.click();
    },
    // 删除
    handleDelete(index: number) {
      const urlList = fileList.value.map((item) => item.url);
      urlList.splice(index, 1);
      fileList.value = urlList;
      message.success('已删除');
    },
    handleDown(url: string) {
      console.log('下载', url);
      downloadByUrl({ url });
    }, // 下载

    // 预览
    handleEye(fileModel: FileModel) {
      const { url, previewTool } = fileModel;
      if (!previewTool) {
        message.warn('暂不支持预览');
        return;
      }
      switch (previewTool) {
        case 'image':
          previewUrl.value = url;
          visible.value = true;
          break;
        default:
          break;
      }
    },
  };
</script>

<style lang="less">
  .UploadFile-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: all 0.3s;
    // 元素之间的间距
    gap: 10px;
  }

  .UploadFile-disabled {
    // 禁用
    opacity: 0.5;

    & .UploadFile-border {
      &:hover {
        border-color: @border-color-base;
        color: @text-color-base;
      }
    }

    & .UploadFile-imbox-buttom {
      display: none;
    }
  }

  .UploadFile-progress {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    min-width: 150px;
    color: @text-color-base;
  }

  .UploadFile-input {
    display: none !important;
  }

  .UploadFile-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 200px;
    max-width: 400px;
  }

  .UploadFile-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;

    &-content {
      flex-grow: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      // 溢出省略号
      white-space: nowrap;
    }

    &-delete {
      color: #fd676f;
      cursor: pointer;

      &:hover {
        color: #f5222d;
      }
    }

    &-download {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
      }
    }

    &-eye {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .UploadFile-border {
    height: 30px;
    padding: 0 15px;
    overflow: hidden;
    transition: all 0.3s;
    border: @border-color-base solid 1px;
    border-radius: 6px;
    background-color: @component-background;
    cursor: pointer;
    // 禁止复制
    user-select: none;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }
</style>
