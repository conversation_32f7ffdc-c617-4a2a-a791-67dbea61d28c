{"description": "建议添加到国际化配置文件中的新键值对", "zh-CN": {"component": {"upload_file": {"parse_error": "文件解析错误", "filename_comma_error": "文件名不能包含逗号", "preview_not_supported": "暂不支持预览", "preview_type_not_supported": "暂不支持该类型文件的预览", "delete_success": "删除成功", "click_upload": "点击上传", "drag_hint": "或 拖拽文件到此处", "file_validation_failed": "文件验证失败"}}}, "en": {"component": {"upload_file": {"parse_error": "File parse error", "filename_comma_error": "Filename cannot contain commas", "preview_not_supported": "Preview not supported", "preview_type_not_supported": "Preview not supported for this file type", "delete_success": "Deleted successfully", "click_upload": "Click to upload", "drag_hint": "or drag files here", "file_validation_failed": "File validation failed"}}}, "usage_instructions": {"description": "如何在项目中使用这些国际化配置", "steps": ["1. 将上述配置添加到对应的语言文件中（如 src/locales/lang/zh-CN/component.json）", "2. 在组件中使用 t('component.upload_file.key') 来获取对应的文本", "3. 替换组件中的硬编码文本"], "example": {"before": "message.warn('暂不支持预览');", "after": "message.warn(t('component.upload_file.preview_not_supported'));"}}}