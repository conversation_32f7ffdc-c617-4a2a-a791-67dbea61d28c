export interface FileModel {
  bucketName: string;
  fileName: string;
  url: string;
  host?: string;
  dir?: string | null;
  icon?: string;
  /** 预览工具类型 */
  previewTool?: string;
  /** 文件后缀 */
  suffix?: string;
  /** 文件大小（字节） */
  size?: number;
}

export interface ResModel {
  code: number;
  data: FileModel;
  msg: string;
}

export interface UploadIdFileModel {
  head: string | undefined;
  tail: string | undefined;
}

// 文件资源对象类型，用于 valueType 为 'object' 时
export interface FileResource {
  id?: number;
  url: string;
}
