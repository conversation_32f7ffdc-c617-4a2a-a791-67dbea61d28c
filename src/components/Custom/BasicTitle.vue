<script lang="ts" setup>
  interface Props {
    title?: string;
    // 是否显示色块
    block?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    block: false,
  });
</script>

<template>
  <div class="basic-title" :class="{ 'show-block': block }">
    <slot>{{ title }}</slot>
    <slot name="extra"></slot>
  </div>
</template>

<style lang="less" scoped>
  .basic-title {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    color: @text-color-base;
    font-size: 16px;
    font-weight: 600;

    &.show-block {
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        transform: translateY(-50%);
        border-radius: 2px;
        background-color: @primary-color;
      }
    }
  }
</style>
