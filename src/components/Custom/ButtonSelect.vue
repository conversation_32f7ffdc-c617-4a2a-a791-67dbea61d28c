<template>
  <div class="button-select-wrapper">
    <div
      v-for="(option, index) in options"
      :key="
        typeof option.value === 'string' || typeof option.value === 'number'
          ? option.value
          : `option-${index}`
      "
      class="button-select-item"
      :class="{
        'button-select-item--active': modelValue === option.value,
        'button-select-item--inactive': modelValue !== option.value,
      }"
      @click="handleChange(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useVModel } from '@vueuse/core';

  interface ButtonSelectOption {
    value: string | number | null | undefined | boolean;
    label: string | number;
  }

  interface ButtonSelectProps {
    modelValue?: string | number | null | undefined | boolean;
    options?: ButtonSelectOption[];
  }

  interface ButtonSelectEmits {
    /**
     * 双向绑定更新事件
     * @param value 更新后的值
     */
    'update:modelValue': [value: string | number | null | undefined | boolean];
    /**
     * 变更回调事件
     * @param value 变更后的值
     */
    change: [value: string | number | null | undefined | boolean];
  }

  const props = withDefaults(defineProps<ButtonSelectProps>(), {
    options: () => [],
  });

  const emit = defineEmits<ButtonSelectEmits>();

  // 使用 VueUse 的 useVModel 实现双向绑定
  const modelValue = useVModel(props, 'modelValue', emit);

  /**
   * 处理选项变更
   * @param val 选中的值
   */
  function handleChange(val: string | number | null | undefined | boolean) {
    modelValue.value = val;
    emit('change', val);
  }
</script>

<style lang="less" scoped>
  .button-select-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .button-select-item {
    padding: 8px 20px;
    transition: all 0.3s ease;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    user-select: none;

    // 默认状态（未选中）
    &--inactive {
      border: 1px solid var(--border-color);
      color: var(--text-color);

      &:hover {
        border: 1px solid @primary-color;
        color: @primary-color;
      }
    }

    // 选中状态
    &--active {
      border: 1px solid @primary-color;
      background-color: fade(@primary-color, 10%);
      color: @primary-color;

      &:hover {
        background-color: fade(@primary-color, 20%);
      }
    }
  }
</style>
