import { h, unref } from 'vue';
import type { VNode, CSSProperties, Ref, ComputedRef } from 'vue';

import { Tag, Badge, Rate, AvatarGroup, Avatar } from 'ant-design-vue';
import { LoadingOutlined } from '@ant-design/icons-vue';
import {
  find,
  isString,
  isNumber,
  cloneDeepWith,
  isArray,
  isObject,
  isBoolean,
  isEmpty,
} from 'lodash-es';

import ShowHtml from './components/ShowHtml.vue';
import { statusBadgeParams, statusBadgeProps, dictToTagProps } from './typing';
import PreviewImage from './components/PreviewImage.vue';
import PreviewImageWithText from './components/PreviewImageWithText.vue';
import WithText from './components/withText.vue';
import UploadFile from '@/components/Form/src/extend/UploadFile.vue';
import DictToTag from './components/DictToTag.vue';

// arr value 转换为大写 的方法
function arrToUpperCase(arr: any[]) {
  return cloneDeepWith(arr, (value) => {
    if (isString(value)) {
      return value.toUpperCase();
    } else if (isNumber(value)) {
      return value.toString();
    }
  });
}

// 获取 label 的实际值，支持 string | Ref<string> | ComputedRef<string>
function getLabelValue(
  label: string | Ref<string> | ComputedRef<string> | undefined,
): string | undefined {
  if (label === undefined) {
    return undefined;
  }
  return unref(label);
}

/**
 * 文字转标签
 */
export function textToTag({ text, color = '#FF851D' }) {
  return h(Tag, { color }, () => text);
}

// 加载中图标
export const loadingIcon = h(LoadingOutlined, { style: { fontSize: '24px' } });

/**
 * 将枚举转换为标签组件
 * @param {object} params
 * @param {string|number} params.text - 需要转换的值
 * @param {Array} params.arr - 包含枚举配置的数组
 * @param {string} [params.defaultText='未知'] - 默认显示的文本
 * @param {boolean} [params.isShow=true] - 是否显示默认标签
 * @returns {VNode|string} 返回标签组件或空字符串
 */
export function showToTag({ text, arr, defaultText = '未知', isShow = false }) {
  // arr value 转换为大写
  const arrClone = arrToUpperCase(arr);
  let textStr = text;
  if (isString(text)) {
    textStr = text.toUpperCase();
  } else if (isNumber(text)) {
    textStr = text.toString();
  }
  const config = find(arrClone, ['value', textStr]);
  if (config || isShow) {
    // 支持 label 为 Ref 或 ComputedRef 的情况
    const label = getLabelValue(config?.label) || textStr || defaultText;
    const color = config?.color || '#44566C';
    return h(Tag, { color }, () => label);
  }
  // 应该返回空的vnode
  return undefined;
}

/**
 * enum 转换为Badge
 * @param {object}
 */
export function showToBadge({ text, arr, defaultText = '未知' }) {
  // arr value 转换为大写
  const arrClone = arrToUpperCase(arr);
  let textStr = text;
  if (isString(text)) {
    textStr = text.toUpperCase();
  } else if (isNumber(text)) {
    textStr = text.toString();
  }
  const config = find(arrClone, ['value', textStr]);
  // 支持 label 为 Ref 或 ComputedRef 的情况
  const label = getLabelValue(config?.label) || textStr || defaultText;
  const color = config?.color || '#44566C';

  return h(Badge, { color, text: label });
}

/**
 * enum 转换为span
 * @param {object}
 */
export function showToText({ text, arr }) {
  // arr value 转换为大写
  const arrClone = arrToUpperCase(arr);
  let textStr = text;
  if (isString(text)) {
    textStr = text.toUpperCase();
  } else if (isNumber(text)) {
    textStr = text.toString();
  }
  const config = find(arrClone, ['value', textStr]);
  // 支持 label 为 Ref 或 ComputedRef 的情况
  const label = getLabelValue(config?.label) || textStr || '未知';
  return label;
}

/**
 * 布尔值返回状态类型
 * @param {boolean}
 * @returns {ElRef}
 */

export function statusBadge(param: statusBadgeParams) {
  if (isBoolean(param)) {
    return param
      ? h(Badge, { color: '#71C922', text: '启用' })
      : h(Badge, { color: '#FF4C52', text: '禁用' });
  }
  if (isEmpty(param)) {
    return h(Badge, { color: '#FF4C52', text: '禁用' });
  }

  const { text, arr } = param as statusBadgeProps;
  let textStr = text;
  if (!isBoolean(text)) {
    textStr = !!text;
  }

  const config = find(arr, ['value', textStr]);
  // 支持 label 为 Ref 或 ComputedRef 的情况
  const label = getLabelValue(config?.label) || textStr || '未知';
  const color = config?.color || '#44566C';
  return h(Badge, { color, text: label });
}

/**
 * @description: 转换字典为标签
 * @param {string} text
 * @param {string} type
 */
export function dictToTag(dictOption: dictToTagProps): VNode {
  return h(DictToTag, dictOption);
}

/**
 * 图片预览
 * @param {string} url
 * @param {CSSProperties} style 支持style 和 内置方案
 * @returns {ElRef}
 */
// 'object-fit': 'cover'
const fallback =
  'data:image/png;base64,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';

// 定义一个type 定义 defaultStyle
type PreviewStyle = 'table' | 'desc' | 'miniDesc' | 'default' | 'miniTable';
export function previewImage(urls: string, style: CSSProperties | PreviewStyle = 'default') {
  const defaultStyles: Record<PreviewStyle, CSSProperties> = {
    table: { width: '110px', height: '70px', objectFit: 'cover' },
    desc: { height: '120px', objectFit: 'cover' },
    miniDesc: { height: '60px', objectFit: 'cover' },
    default: { objectFit: 'cover' },
    miniTable: { width: '80px', height: '50px', objectFit: 'cover' },
  };

  const mergedStyle = isString(style)
    ? { ...defaultStyles.default, ...defaultStyles[style] }
    : { ...defaultStyles.default, ...style };

  const urlList = urls
    ?.split(',')
    .map((u) => u.trim())
    .filter(Boolean);

  if (urlList && urlList.length > 0) {
    return h(PreviewImage, {
      urlList,
      style: mergedStyle,
      fallback,
      placeholder: true,
    });
  }

  return h(
    'div',
    {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        margin: '0 auto',
        fontSize: '12px',
        color: '#999',
        ...mergedStyle,
      },
    },
    '暂无图片',
  );
}

/**
 * 头像预览
 * 传入一个图片地址，或者逗号分割的多个图片地址
 * 返回一个图片预览组件
 * @param {string} urls
 * @returns {ElRef}
 */
export function previewAvatar(urls) {
  if (urls) {
    const urlList = urls.split(',');
    const avatarList = urlList.map((item) => {
      return h(Avatar, {
        size: 38,
        src: item,
      });
    });
    return h(
      AvatarGroup,
      {
        shape: 'square',
      },
      () => avatarList,
    );
  } else {
    return h(Avatar, {
      size: 38,
      shape: 'square',
    });
  }
}

/**
 * 图片预览
 * @param {string} urls
 * @param {string} showText
 * @returns {ElRef}
 */
export function previewImageWithText(urls = '', showText = '查看图片', notImageText = '暂无图片') {
  if (urls) {
    const urlList = urls.split(',');
    return h(PreviewImageWithText, {
      urlList,
      fallback,
      showText,
    });
  } else {
    return h('span', notImageText);
  }
}

/**
 * 下划线按钮
 * @param {string} showText
 * @param {function} callBack
 * @returns {ElRef}
 */
export function withText(options: { showText: string; callBack: () => void }) {
  const { showText, callBack } = options;
  return h(WithText, {
    showText,
    onRunCallBack: callBack,
  });
}

/**
 * @description: 直接展示html
 * @param {string} html
 */
export function htmlRender(html: string): VNode {
  return h(ShowHtml, { html });
}

/**
 * @description: 评分
 * @param {number} value
 */
export function rateRender(value: number): VNode {
  // 异常处理 空值 数组 空字符串 对象 等 都把值设置为0
  if (!value || isArray(value) || isObject(value)) {
    value = 0;
  }
  // 如果是字符串类型，转换为数字类型
  if (isString(value)) {
    value = Number(value);
  }
  return h(Rate, { value, disabled: true });
}

/**
 * @description: 文件预览
 */

export function fileRender(fileUrls: string): VNode {
  return h(UploadFile, { value: fileUrls, preview: true });
}
