import { computed, unref, type Ref, type ComputedRef } from 'vue';
import { useOnlineI18n } from './useOnlineI18n';
import { CreateMapParams } from '#/utils';
import { isArray } from 'lodash-es';

/**
 * 用于页面回显的映射标签查找 Hook
 * 根据 value 从映射中查找对应的 label（支持国际化）
 *
 * @param list 映射参数列表
 * @returns 包含查找函数的对象
 *
 * @example
 * ```vue
 * <script setup>
 * import { useMapLabel } from '@/hooks/web/useMapLabel';
 * import { AppPublishPlatformList } from '@/maps/prMaps';
 *
 * const { getLabel } = useMapLabel(AppPublishPlatformList);
 * const platformLabel = getLabel(computed(() => apiResult.platform));
 * </script>
 *
 * <template>
 *   <span>{{ platformLabel }}</span>
 * </template>
 * ```
 */
export function useMapLabel(list: CreateMapParams[]) {
  const { t } = useOnlineI18n();

  // 静态预处理：分离静态数据和国际化数据，避免不必要的响应式开销
  const staticMap = new Map<string, { value: any; label: string; color: string; extend: any }>();
  const i18nMap = new Map<
    string,
    { value: any; i18nLabel: [string, string?]; color: string; extend: any }
  >();

  // 一次性处理，list 不会变化
  list.forEach(([value, label, color, extend]) => {
    const baseOption = { value, color, extend };

    if (typeof label === 'string') {
      staticMap.set(value, { ...baseOption, label });
    } else if (isArray(label)) {
      i18nMap.set(value, { ...baseOption, i18nLabel: label });
    } else {
      staticMap.set(value, { ...baseOption, label: label as string });
    }
  });

  /**
   * 根据 value 获取对应的响应式标签
   * @param value 要查找的值（支持任意类型，会自动转换为字符串）
   * @returns 响应式的标签字符串
   */
  const getLabel = (value: any | Ref<any>): ComputedRef<string> => {
    return computed(() => {
      try {
        const val = unref(value);

        // 异常处理：检查无效值
        if (val === null || val === undefined || val === '') {
          return '';
        }

        // 类型安全：确保 val 是字符串
        const searchKey = String(val);

        // 先查静态数据
        if (staticMap.has(searchKey)) {
          return staticMap.get(searchKey)!.label;
        }

        // 再查国际化数据
        if (i18nMap.has(searchKey)) {
          const option = i18nMap.get(searchKey)!;
          const translatedLabel = t(option.i18nLabel);
          // 如果 label 是响应式的，获取其值
          if (typeof translatedLabel === 'object' && 'value' in translatedLabel) {
            return translatedLabel.value || '';
          }
          return (translatedLabel as string) || '';
        }

        // 未找到匹配项，返回原值（转为字符串）
        return searchKey;
      } catch (error) {
        // 异常兜底：记录错误并返回空字符串
        console.warn('[useMapLabel] getLabel error:', error, 'value:', value);
        return '';
      }
    });
  };

  /**
   * 根据 value 同步获取标签（非响应式）
   * @param value 要查找的值（支持任意类型，会自动转换为字符串）
   * @returns 标签字符串
   */
  const getLabelSync = (value: any): string => {
    try {
      // 异常处理：检查无效值
      if (value === null || value === undefined || value === '') {
        return '';
      }

      // 类型安全：确保 value 是字符串
      const searchKey = String(value);

      // 先查静态数据
      if (staticMap.has(searchKey)) {
        return staticMap.get(searchKey)!.label;
      }

      // 再查国际化数据
      if (i18nMap.has(searchKey)) {
        const option = i18nMap.get(searchKey)!;
        const translatedLabel = t(option.i18nLabel);
        // 如果 label 是响应式的，获取其值
        if (typeof translatedLabel === 'object' && 'value' in translatedLabel) {
          return translatedLabel.value || '';
        }
        return (translatedLabel as string) || '';
      }

      // 未找到匹配项，返回原值（转为字符串）
      return searchKey;
    } catch (error) {
      // 异常兜底：记录错误并返回空字符串
      console.warn('[useMapLabel] getLabelSync error:', error, 'value:', value);
      return '';
    }
  };

  /**
   * 获取所有映射的标签对象
   * @returns 包含所有 value -> label 映射的对象
   */
  const getAllLabels = (): ComputedRef<Record<string, string>> => {
    return computed(() => {
      try {
        const result: Record<string, string> = {};

        // 处理静态数据
        staticMap.forEach((option, value) => {
          try {
            if (value && option?.label) {
              result[value] = option.label;
            }
          } catch (error) {
            console.warn('[useMapLabel] getAllLabels static error:', error, 'value:', value);
          }
        });

        // 处理国际化数据
        i18nMap.forEach((option, value) => {
          try {
            if (value && option?.i18nLabel) {
              const translatedLabel = t(option.i18nLabel);
              if (typeof translatedLabel === 'object' && 'value' in translatedLabel) {
                result[value] = translatedLabel.value || '';
              } else {
                result[value] = (translatedLabel as string) || '';
              }
            }
          } catch (error) {
            console.warn('[useMapLabel] getAllLabels i18n error:', error, 'value:', value);
          }
        });

        return result;
      } catch (error) {
        console.warn('[useMapLabel] getAllLabels error:', error);
        return {};
      }
    });
  };

  return {
    getLabel,
    getLabelSync,
    getAllLabels,
  };
}

/**
 * 简化版本：直接根据映射和值获取标签
 * @param list 映射参数列表
 * @param value 要查找的值
 * @returns 响应式的标签字符串
 *
 * @example
 * ```vue
 * <script setup>
 * const platformLabel = useMapLabelDirect(AppPublishPlatformList, apiResult.platform);
 * </script>
 *
 * <template>
 *   <span>{{ platformLabel }}</span>
 * </template>
 * ```
 */
export function useMapLabelDirect(
  list: CreateMapParams[],
  value: any | Ref<any>,
): ComputedRef<string> {
  const { getLabel } = useMapLabel(list);
  return getLabel(value);
}
