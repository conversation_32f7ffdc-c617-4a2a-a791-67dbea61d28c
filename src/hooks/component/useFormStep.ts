import type { ComputedRef } from 'vue';
import { computed, unref, ref } from 'vue';

/**
 * 步骤配置接口
 */
interface StepConfig {
  /** 下一步回调函数 */
  nextFn?: (data?: any) => void | Promise<void>;
  /** 上一步回调函数 */
  prevFn?: (data?: any) => void | Promise<void>;
  /** 重置回调函数 */
  redoFn?: () => void | Promise<void>;
  /** 步骤总数，默认为 5 */
  stepNum?: number;
  /** 默认显示第几步，默认为 1 */
  defaultStep?: number;
  /** 是否允许跳跃式导航，默认为 false */
  allowJump?: boolean;
}

/**
 * 步骤显示状态接口
 */
interface ShowStep {
  /** 当前步骤索引（从0开始） */
  current: number;
  /** 当前步骤编号（从1开始） */
  currentStep: number;
  /** 总步骤数 */
  total: number;
  /** 是否为第一步 */
  isFirst: boolean;
  /** 是否为最后一步 */
  isLast: boolean;
  /** 动态生成的步骤状态，如 step1, step2... */
  [key: string]: boolean | number;
}

/**
 * 步骤操作方法接口
 */
interface StepActions {
  /** 上一步 */
  prevStep: (data?: any) => Promise<void>;
  /** 下一步 */
  nextStep: (data?: any) => Promise<void>;
  /** 重置到第一步 */
  redoStep: () => Promise<void>;
  /** 跳转到指定步骤 */
  setStep: (step: number) => void;
  /** 获取当前步骤 */
  getCurrentStep: () => number;
  /** 检查是否可以跳转到指定步骤 */
  canGoToStep: (step: number) => boolean;
}

/**
 * useFormStep 返回类型
 */
type UseFormStepReturn = [ComputedRef<ShowStep>, StepActions];

/**
 * 表单步骤管理 Hook
 * @param stepConfig 步骤配置
 * @returns 返回步骤状态和操作方法
 */
export function useFormStep(stepConfig?: StepConfig): UseFormStepReturn {
  // 参数验证和默认值设置
  const stepNum = Math.max(1, stepConfig?.stepNum || 5);
  const defaultStep = Math.max(1, Math.min(stepConfig?.defaultStep || 1, stepNum));
  const allowJump = stepConfig?.allowJump ?? false;

  // 响应式状态
  const current = ref(defaultStep);
  const lastCurrent = ref(defaultStep);

  /**
   * 计算步骤显示状态
   */
  const showStep = computed<ShowStep>(() => {
    const cur = unref(current);
    const last = unref(lastCurrent);

    const stepState: ShowStep = {
      current: cur - 1, // 从0开始的索引
      currentStep: cur, // 从1开始的步骤号
      total: stepNum,
      isFirst: cur === 1,
      isLast: cur === stepNum,
    };

    // 动态生成步骤状态
    for (let index = 1; index <= stepNum; index++) {
      const stepKey = `step${index}`;
      const initKey = `init${index}`;
      stepState[stepKey] = cur === index;
      stepState[initKey] = last >= index;
    }

    return stepState;
  });
  /**
   * 检查是否可以跳转到指定步骤
   */
  const canGoToStep = (step: number): boolean => {
    if (step < 1 || step > stepNum) return false;
    if (allowJump) return true;
    return step <= lastCurrent.value;
  };

  /**
   * 获取当前步骤
   */
  const getCurrentStep = (): number => current.value;

  /**
   * 上一步操作
   */
  const prevStep = async (data?: any): Promise<void> => {
    if (current.value <= 1) {
      console.warn('已经是第一步，无法继续向前');
      return;
    }

    try {
      await stepConfig?.prevFn?.(data);
      current.value--;
    } catch (error) {
      console.error('上一步操作失败:', error);
      throw error;
    }
  };

  /**
   * 下一步操作
   */
  const nextStep = async (data?: any): Promise<void> => {
    if (current.value >= stepNum) {
      console.warn('已经是最后一步，无法继续向后');
      return;
    }

    try {
      await stepConfig?.nextFn?.(data);
      current.value++;
      if (lastCurrent.value < current.value) {
        lastCurrent.value = current.value;
      }
    } catch (error) {
      console.error('下一步操作失败:', error);
      throw error;
    }
  };

  /**
   * 跳转到指定步骤
   */
  const setStep = (step: number): void => {
    if (!canGoToStep(step)) {
      console.warn(`无法跳转到步骤 ${step}，请检查步骤范围或权限`);
      return;
    }

    current.value = step;
    if (lastCurrent.value < current.value) {
      lastCurrent.value = current.value;
    }
  };

  /**
   * 重置到第一步
   */
  const redoStep = async (): Promise<void> => {
    try {
      await stepConfig?.redoFn?.();
      current.value = 1;
      lastCurrent.value = 1;
    } catch (error) {
      console.error('重置操作失败:', error);
      throw error;
    }
  };

  const actions: StepActions = {
    prevStep,
    nextStep,
    redoStep,
    setStep,
    getCurrentStep,
    canGoToStep,
  };

  return [showStep, actions];
}
