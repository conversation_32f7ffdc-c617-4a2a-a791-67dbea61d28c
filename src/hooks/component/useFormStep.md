# useFormStep Hook 文档

## 概述

`useFormStep` 是一个用于管理表单步骤的 Vue 3 Composition API Hook，提供了完整的步骤导航、状态管理和回调处理功能。

## 主要特性

### ✨ 新增特性
- **类型安全**: 完整的 TypeScript 类型定义
- **异步支持**: 支持异步回调函数和错误处理
- **边界检查**: 完善的参数验证和边界条件处理
- **跳跃导航**: 可配置的跳跃式步骤导航
- **状态丰富**: 提供更多有用的步骤状态信息
- **错误处理**: 内置错误处理和日志记录

### 🔧 优化改进
- **参数验证**: 自动验证和修正配置参数
- **状态计算**: 优化的响应式状态计算逻辑
- **方法命名**: 更清晰的方法命名和功能划分
- **文档完善**: 详细的 JSDoc 注释和使用说明

## API 接口

### StepConfig 配置选项

```typescript
interface StepConfig {
  /** 下一步回调函数 */
  nextFn?: (data?: any) => void | Promise<void>;
  /** 上一步回调函数 */
  prevFn?: (data?: any) => void | Promise<void>;
  /** 重置回调函数 */
  redoFn?: () => void | Promise<void>;
  /** 步骤总数，默认为 5 */
  stepNum?: number;
  /** 默认显示第几步，默认为 1 */
  defaultStep?: number;
  /** 是否允许跳跃式导航，默认为 false */
  allowJump?: boolean;
}
```

### ShowStep 状态对象

```typescript
interface ShowStep {
  /** 当前步骤索引（从0开始） */
  current: number;
  /** 当前步骤编号（从1开始） */
  currentStep: number;
  /** 总步骤数 */
  total: number;
  /** 是否为第一步 */
  isFirst: boolean;
  /** 是否为最后一步 */
  isLast: boolean;
  /** 动态生成的步骤状态 */
  step1: boolean;  // 当前是否为步骤1
  step2: boolean;  // 当前是否为步骤2
  // ... 更多步骤
  init1: boolean;  // 步骤1是否已访问过
  init2: boolean;  // 步骤2是否已访问过
  // ... 更多初始化状态
}
```

### StepActions 操作方法

```typescript
interface StepActions {
  /** 上一步 */
  prevStep: (data?: any) => Promise<void>;
  /** 下一步 */
  nextStep: (data?: any) => Promise<void>;
  /** 重置到第一步 */
  redoStep: () => Promise<void>;
  /** 跳转到指定步骤 */
  setStep: (step: number) => void;
  /** 获取当前步骤 */
  getCurrentStep: () => number;
  /** 检查是否可以跳转到指定步骤 */
  canGoToStep: (step: number) => boolean;
}
```

## 使用示例

### 基础使用

```typescript
import { useFormStep } from '@/hooks/component/useFormStep';

const [stepState, stepActions] = useFormStep({
  stepNum: 4,
  defaultStep: 1,
});

// 获取状态
console.log(stepState.value.currentStep); // 当前步骤号
console.log(stepState.value.isFirst);     // 是否第一步
console.log(stepState.value.step1);       // 是否为步骤1

// 步骤操作
await stepActions.nextStep();              // 下一步
await stepActions.prevStep();              // 上一步
stepActions.setStep(3);                    // 跳转到步骤3
await stepActions.redoStep();              // 重置
```

### 带验证的高级使用

```typescript
const [stepState, stepActions] = useFormStep({
  stepNum: 3,
  allowJump: true,
  
  nextFn: async (formData) => {
    // 表单验证
    if (!formData?.isValid) {
      throw new Error('请完善表单信息');
    }
    
    // 提交数据
    await submitFormData(formData);
  },
  
  prevFn: async (data) => {
    // 保存草稿
    await saveDraft(data);
  },
  
  redoFn: async () => {
    // 清理数据
    await clearFormData();
  },
});
```

## 最佳实践

1. **错误处理**: 始终使用 try-catch 包装异步操作
2. **数据传递**: 通过参数传递步骤间的数据
3. **权限控制**: 使用 `canGoToStep` 检查跳转权限
4. **状态监听**: 监听 `stepState` 的变化来更新 UI
5. **回调优化**: 在回调中进行必要的验证和数据处理

## 迁移指南

### 从旧版本迁移

1. **返回值变化**: 操作方法现在返回 Promise
2. **新增方法**: `getCurrentStep` 和 `canGoToStep`
3. **状态扩展**: 新增 `currentStep`、`total`、`isFirst`、`isLast`
4. **配置选项**: 新增 `allowJump` 配置

### 兼容性处理

```typescript
// 旧版本
const [state, { nextStep }] = useFormStep();
nextStep(data); // 同步调用

// 新版本
const [state, { nextStep }] = useFormStep();
await nextStep(data); // 异步调用
```
