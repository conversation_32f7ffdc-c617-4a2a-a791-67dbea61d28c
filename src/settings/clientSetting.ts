import { find } from 'lodash-es';
import { getUserInfo, userChangePass } from '@/api/sys/user';
import { getClientId } from '@/utils/auth';
import { PageEnum } from '@/enums/pageEnum';

interface Client {
  clientId: string;
  clientSecret: string;
  clientName: string;
  label: string;
  scope: string;
  apiUserInfo: () => Promise<any>;
  apiPassWord: (data: any) => Promise<any>;
  homePath: string;
  // 前端 aes密钥
  aesKey?: string;
}

interface ClientStore extends Client {
  Authorization: string;
}

export const clientList: Client[] = [
  {
    clientId: 'pig',
    clientSecret: 'pig',
    clientName: '运营管理',
    label: '运营管理',
    scope: 'server',
    apiUserInfo: getUserInfo,
    apiPassWord: userChangePass,
    homePath: PageEnum.BASE_HOME,
    aesKey: 'acdefhikmnpqrstv',
  },
];

export const clientSelectList = clientList.map((item) => {
  return {
    label: item.label,
    value: item.clientId,
  };
});

export function getClient(
  clientId: string | null | unknown = getClientId(),
): ClientStore | undefined {
  const foundClient = find(clientList, { clientId }) as Client;
  if (!foundClient) {
    return undefined;
  }
  const Authorization =
    'Basic ' + window.btoa(`${foundClient.clientId}:${foundClient.clientSecret}`);

  return {
    ...foundClient,
    Authorization,
  };
}
