<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="服务详情">
    <template #header>
      <BasicUser :username="get(apiResult, 'name')">
        <template #extra>
          <div> 此处功能扩展</div>
        </template>
        <component :is="showToBadge({ text: get(apiResult, 'status'), arr: [] })" />
      </BasicUser>
    </template>
    <Tabs>
      <TabPane key="baseInfo" tab="基本信息">
        <DividerTitle :line="false" title="基础信息" />
        <Card>
          <Description :column="2" :schema="[]" :data="apiResult" :bordered="false" />
        </Card>
      </TabPane>
      <TabPane key="signedOrder" tab="关联服务" />
      <TabPane key="appointmentRecord" tab="佣金明细">佣金明细</TabPane>
    </Tabs>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { Description } from '@/components/Description';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { showToBadge } from '@/components/RenderVnode';
  import { get } from 'lodash-es';
  import { Card, Tabs, TabPane } from 'ant-design-vue';
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => ({}),
    params,
  });
</script>

<style lang="less" scoped></style>
