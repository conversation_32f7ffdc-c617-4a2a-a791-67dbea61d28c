<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="详情">
    <template #header>
      <BasicUser :username="get(apiResult, 'name')">
        <template #extra>
          <div> 此处功能扩展</div>
        </template>
        <component :is="showToBadge({ text: get(apiResult, 'status'), arr: [] })" />
      </BasicUser>
    </template>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { showToBadge } from '@/components/RenderVnode';
  import { get } from 'lodash-es';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => ({}),
    params,
  });
</script>

<style lang="less" scoped></style>
