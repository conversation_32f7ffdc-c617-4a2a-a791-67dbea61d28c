<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="设备详情">
    <template #extra>
      <div class="flex items-center gap-4">
        <a-button type="primary">绑定终端客户</a-button>
        <a-button danger>解除关联合作伙伴</a-button>
        <a-button>关联合作伙伴</a-button>
      </div>
    </template>

    <div class="flex flex-col gap-4">
      <!-- 设备基础信息卡片 -->
      <a-card title="设备基础信息">
        <Description
          :data="deviceBasicInfo"
          :schema="basicInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 绑定终端客户信息卡片 -->
      <a-card title="绑定终端客户信息">
        <Description
          :data="customerInfo"
          :schema="customerInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 设备详细信息卡片 -->
      <a-card title="设备详细信息">
        <Description
          :data="deviceDetailInfo"
          :schema="detailInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 设备状态信息卡片 -->
      <DeviceStatusCard :status-info-schema="statusInfoSchema" />

      <!-- 连接配件信息卡片 -->
      <ConnectionAccessoriesCard :accessory-columns="accessoryColumns" />

      <!-- 远程维护卡片 -->
      <RemoteMaintenanceCard />

      <!-- 运程维护记录卡片 -->
      <MaintenanceRecordsCard :maintenance-columns="maintenanceColumns" />

      <!-- 关联合作伙伴记录内容 -->
      <PartnerRecordsCard :partner-columns="partnerColumns" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiGetDeviceInfoById } from '@/api/op/de';
  import { Description } from '@/components/Description';
  import {
    basicInfoSchema,
    detailInfoSchema,
    statusInfoSchema,
    customerInfoSchema,
    accessoryColumns,
    maintenanceColumns,
    partnerColumns,
  } from './info-bind.schema';
  import { computed } from 'vue';
  import ConnectionAccessoriesCard from './components/ConnectionAccessoriesCard.vue';
  import RemoteMaintenanceCard from './components/RemoteMaintenanceCard.vue';
  import MaintenanceRecordsCard from './components/MaintenanceRecordsCard.vue';
  import PartnerRecordsCard from './components/PartnerRecordsCard.vue';
  import DeviceStatusCard from './components/DeviceStatusCard.vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;

  const {
    reload: _reload,
    loading,
    apiResult: _,
  } = useApiLoading({
    api: async () => {
      if (params.id) {
        return await apiGetDeviceInfoById(params.id as string);
      }
      return {};
    },
    params,
  });

  // 设备基础信息数据
  const deviceBasicInfo = computed(() => ({
    deviceSn: '123665456814422',
    hardwareProductId: 'FH156A14-S',
    platformRegistrationTime: '2023-12-16 08:30:00',
    partnerName: 'DCM',
    operatorName: 'Dylan',
    lastOperationTime: '2023-12-16 08:30:00',
  }));

  // 绑定终端客户信息数据
  const customerInfo = computed(() => ({
    customerName: '客户名称1',
    customerType: '客户类型',
    bindTime: '2023-12-16 08:30:00',
    operator: 'Dylan',
  }));

  // 设备详细信息数据
  const deviceDetailInfo = computed(() => ({
    firmwareVersion: 'v.0.110',
    systemVersion: 'Android 7.1.2',
    screenSize1: '-',
    screenSize2: '-',
    resolution1: '-',
    resolution2: '-',
    imei: '-',
    macAddress: '-',
    wifiMacAddress: '-',
    bluetoothAddress: '-',
  }));
</script>

<style lang="less" scoped></style>
