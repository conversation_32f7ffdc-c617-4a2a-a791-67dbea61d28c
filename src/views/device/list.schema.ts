import { BasicColumn, FormSchema } from '@/components/Table';
import { apiInfoList } from '@/api/op/pr';
import { apiGetCustomerPage } from '@/api/op/customer';
import { getAllPageData } from '@/utils/other';

export const basicColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '设备SN',
    dataIndex: 'deviceSn',
    width: 150,
  },
  {
    title: '硬件产品号',
    dataIndex: 'productModel',
    width: 120,
  },
  {
    title: '合作伙伴名称',
    dataIndex: 'partnerName',
    width: 150,
  },
  {
    title: '注册时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '在线状态',
    dataIndex: 'onlineStatus',
    width: 100,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'deviceSN',
    label: '设备SN',
    component: 'Input',
    componentProps: {
      placeholder: '请输入设备SN',
    },
  },
  {
    field: 'model',
    label: '硬件产品号',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: (params) => apiInfoList(params),
        labelField: 'model',
        valueField: 'model',
        params: {
          typeId: formModel?._categoryId,
        },
      };
    },
  },
  {
    field: 'partnerId',
    label: '合作伙伴名称',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择合作伙伴',
      api: (params) => getAllPageData(apiGetCustomerPage, params),
      params: {
        type: 'PARTNER',
      },
      labelField: 'linkman',
      valueField: 'id',
    },
  },
];
