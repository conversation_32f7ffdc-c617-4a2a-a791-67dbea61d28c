import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '设备SN',
    dataIndex: 'deviceSn',
    width: 150,
  },
  {
    title: '硬件产品号',
    dataIndex: 'hardwareProductId',
    width: 120,
  },
  {
    title: '合作伙伴名称',
    dataIndex: 'partnerName',
    key: 'partnerName',
    width: 150,
  },
  {
    title: '注册时间',
    dataIndex: 'registrationTime',
    width: 160,
  },
  {
    title: '在线状态',
    dataIndex: 'onlineStatus',
    key: 'onlineStatus',
    width: 100,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'deviceSn',
    label: '设备SN',
    component: 'Input',
    componentProps: {
      placeholder: '请输入设备SN',
    },
  },
  {
    field: 'hardwareProductId',
    label: '硬件产品号',
    component: 'Select',
    componentProps: {
      placeholder: '请选择硬件产品号',
      options: [
        { label: 'FH156A14-S', value: 'FH156A14-S' },
        { label: 'FH156A14-M', value: 'FH156A14-M' },
        { label: 'FH156A14-L', value: 'FH156A14-L' },
      ],
      showSearch: true,
      allowClear: true,
    },
  },
  {
    field: 'partnerName',
    label: '合作伙伴名称',
    component: 'Select',
    componentProps: {
      placeholder: '请选择合作伙伴',
      options: [
        { label: 'DCM', value: 'DCM' },
        { label: '合作伙伴2', value: '合作伙伴2' },
        { label: '合作伙伴3', value: '合作伙伴3' },
      ],
      showSearch: true,
      allowClear: true,
    },
  },
];
