import { DescItem } from '@/components/Description';
import { BasicColumn } from '@/components/Table';
import { showToTag } from '@/components/RenderVnode';
import { h } from 'vue';
import { Tag, Button } from 'ant-design-vue';

// 设备基础信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'deviceSn',
    label: '设备SN',
  },
  {
    field: 'hardwareProductId',
    label: '硬件产品号',
  },
  {
    field: 'platformRegistrationTime',
    label: '平台注册时间',
  },
  {
    field: 'partnerName',
    label: '关联合作伙伴',
  },
  {
    field: 'operatorName',
    label: '操作人',
  },
  {
    field: 'lastOperationTime',
    label: '最后操作时间',
  },
];

// 设备详细信息
export const detailInfoSchema: DescItem[] = [
  {
    field: 'firmwareVersion',
    label: '固件版本',
  },
  {
    field: 'systemVersion',
    label: '系统版本',
  },
  {
    field: 'screenSize1',
    label: '屏幕尺寸1',
  },
  {
    field: 'screenSize2',
    label: '屏幕尺寸2',
  },
  {
    field: 'resolution1',
    label: '分辨率1',
  },
  {
    field: 'resolution2',
    label: '分辨率2',
  },
  {
    field: 'imei',
    label: 'IMEI',
  },
  {
    field: 'macAddress',
    label: '以太网MAC地址',
  },
  {
    field: 'wifiMacAddress',
    label: 'Wi-Fi MAC地址',
  },
  {
    field: 'bluetoothAddress',
    label: '蓝牙地址',
  },
];

// 设备状态信息
export const statusInfoSchema: DescItem[] = [
  {
    field: 'imsi',
    label: 'IMSI',
  },
  {
    field: 'iccid',
    label: 'ICCID',
  },
  {
    field: 'networkStatus',
    label: '网络状态',
    render: (val: string) => {
      return showToTag({
        text: val,
        arr: [
          { value: 'Wi-Fi', label: 'Wi-Fi', color: 'green' },
          { value: '4G', label: '4G', color: 'blue' },
          { value: 'offline', label: '离线', color: 'red' },
        ],
      });
    },
  },
  {
    field: 'wifiName',
    label: 'Wi-Fi',
  },
  {
    field: 'mobileOperator',
    label: '移动数据',
  },
  {
    field: 'batteryLevel',
    label: '电池电量',
    render: (val: number) => {
      if (val === undefined || val === null) return '--';
      return `${val}%`;
    },
  },
  {
    field: 'storage',
    label: '存储',
  },
  {
    field: 'cpuUsage',
    label: '亮度',
    render: (val: number) => {
      if (val === undefined || val === null) return '--';
      return `${val}%`;
    },
  },
  {
    field: 'baseStationStatus',
    label: '基站状态',
    render: (val: string) => {
      return showToTag({
        text: val,
        arr: [
          { value: 'online', label: '在线', color: 'green' },
          { value: 'offline', label: '离线', color: 'red' },
        ],
      });
    },
  },
  {
    field: 'cateringServiceStatus',
    label: 'Catering 服务连接状态',
    render: (val: string) => {
      return showToTag({
        text: val,
        arr: [
          { value: 'connected', label: '在线', color: 'green' },
          { value: 'disconnected', label: '离线', color: 'red' },
        ],
      });
    },
  },
  {
    field: 'memoryUsage',
    label: '内存',
  },
  {
    field: 'notificationVolume',
    label: '通知音量',
    render: (val: number) => {
      if (val === undefined || val === null) return '--';
      return `${val}%`;
    },
  },
];

// 连接配件信息表格列定义
export const accessoryColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'key',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    width: 80,
  },
  {
    title: '连接状态',
    dataIndex: 'connectionStatus',
    width: 80,
    customRender: ({ text }) => {
      const color = text === '已连接' ? 'green' : text === '未连接' ? 'red' : 'default';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '连接方式',
    dataIndex: 'connectionMethod',
    width: 80,
  },
  {
    title: '设备型号',
    dataIndex: 'deviceId',
    width: 80,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 80,
  },
  {
    title: 'IP地址',
    dataIndex: 'ipAddress',
    width: 100,
  },
  {
    title: 'MAC地址',
    dataIndex: 'macAddress',
    width: 100,
  },
  {
    title: '蓝牙bd_addr',
    dataIndex: 'bluetoothId',
    width: 100,
  },
  {
    title: '其他唯一标识',
    dataIndex: 'uniqueId',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 120,
    fixed: 'right',
    customRender: ({ record }) => {
      const statusText = record.status;
      const color =
        statusText === '激活打印' ? 'orange' : statusText === '打开钱箱' ? 'blue' : 'default';

      return h('div', { class: 'flex gap-2' }, [
        h(
          Button,
          {
            type: 'primary',
            size: 'small',
            style: {
              backgroundColor:
                color === 'orange' ? '#fa8c16' : color === 'blue' ? '#1890ff' : undefined,
            },
          },
          () => statusText,
        ),
        h(
          Button,
          {
            type: 'link',
            size: 'small',
          },
          () => '其他操作',
        ),
      ]);
    },
  },
];

// 运程维护记录表格列定义
export const maintenanceColumns: BasicColumn[] = [
  {
    title: '时间',
    dataIndex: 'time',
    width: 160,
  },
  {
    title: '操作类型',
    dataIndex: 'operationType',
    width: 100,
  },
  {
    title: '操作内容',
    dataIndex: 'operationContent',
    width: 200,
  },
  {
    title: '操作IP地址',
    dataIndex: 'operationIp',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      const color = text === '成功' ? 'green' : text === '失败' ? 'red' : 'default';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 120,
  },
];

// 关联合作伙伴记录表格列定义
export const partnerColumns: BasicColumn[] = [
  {
    title: '时间',
    dataIndex: 'time',
    width: 160,
  },
  {
    title: '关联解除关联',
    dataIndex: 'operationType',
    width: 120,
  },
  {
    title: '合作伙伴名称',
    dataIndex: 'partnerName',
    width: 150,
  },
  {
    title: '原因',
    dataIndex: 'reason',
    width: 100,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 80,
    fixed: 'right',
    customRender: () => {
      return h(
        Button,
        {
          type: 'link',
          size: 'small',
          style: { color: '#fa8c16' },
        },
        () => '查看',
      );
    },
  },
];
