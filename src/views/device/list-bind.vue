<template>
  <page-wrapper>
    <template #header>
      <ButtonSelect v-model="searchInfo.status" class="p-1.5" :options="statusOptions" />
    </template>

    <BasicTable @register="registerTable" @selection-change="handleSelectionChange">
      <template #tableTitle>
        <div class="flex items-center gap-4">
          <a-space>
            <a-button
              type="primary"
              @click="method.handleBatchBind"
              :disabled="!hasSelected"
              preIcon="ant-design:link-outlined"
            >
              批量绑定客户
            </a-button>
            <a-button
              type="primary"
              @click="method.handleExport"
              :loading="exportLoading"
              preIcon="ant-design:export-outlined"
            >
              导出
            </a-button>
          </a-space>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
        <template v-if="column.key === 'onlineStatus'">
          <a-badge
            :status="record.onlineStatus ? 'success' : 'default'"
            :text="record.onlineStatus ? '在线' : '离线'"
          />
        </template>
        <template v-if="column.key === 'partnerName'">
          <span v-if="record.partnerName">{{ record.partnerName }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import ButtonSelect from '@/components/Custom/ButtonSelect.vue';
  import { ref, computed } from 'vue';
  import { basicColumns, searchSchema } from '@/views/device/list-bind.schema';
  import { message, Modal } from 'ant-design-vue';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  // 状态选项
  const statusOptions = [
    { value: '', label: '全部设备 (10000010)' },
    { value: 'BOUND', label: '已关联合作伙伴 (10000000)' },
    { value: 'UNBOUND', label: '未关联合作伙伴 (10)' },
  ];

  // 搜索信息
  const searchInfo = ref({
    status: '',
    queryType: 'CUSTOMER_EQUIPMENT',
  });

  // 选中的行
  const selectedRowKeys = ref<string[]>([]);
  const hasSelected = computed(() => selectedRowKeys.value.length > 0);
  const exportLoading = ref(false);

  // 表格配置
  const [registerTable, { reload, getSearchInfo }] = useTable({
    api: async (params) => {
      // 模拟API调用
      console.log('API params:', params);
      return {
        records: [
          {
            id: '1',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: 'DCM',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '2',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: 'DCM',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '3',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: 'DCM',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '4',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: 'DCM',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '5',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: '',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '6',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: '',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
          {
            id: '7',
            deviceSn: '1235611254552',
            hardwareProductId: 'FH156A14-S',
            partnerName: '合作伙伴2',
            registrationTime: '2023-12-16 08:30:00',
            onlineStatus: true,
          },
        ],
        total: 7,
      };
    },
    rowKey: 'id',
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
      autoSubmitOnEnter: true,
    },
    searchInfo,
    useSearchForm: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {
      width: 100,
    },
  });

  // 处理选择变化
  function handleSelectionChange({ keys }: { keys: string[] }) {
    selectedRowKeys.value = keys;
  }

  const method = {
    /** 批量绑定客户 */
    handleBatchBind() {
      if (!hasSelected.value) {
        message.warning('请先选择要绑定的设备');
        return;
      }
      Modal.info({
        title: '批量绑定客户',
        content: `已选择 ${selectedRowKeys.value.length} 台设备进行批量绑定`,
        onOk() {
          // 这里实现批量绑定逻辑
          message.success('批量绑定成功');
          selectedRowKeys.value = [];
          reload();
        },
      });
    },

    /** 导出 */
    async handleExport() {
      exportLoading.value = true;
      try {
        const searchInfo = getSearchInfo();
        // 这里实现导出逻辑
        console.log('Export params:', searchInfo);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败');
      } finally {
        exportLoading.value = false;
      }
    },

    /** 详情 */
    detail: (record: Recordable) => {
      go(`/device/list-bind/${record.id}`);
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: method.detail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
