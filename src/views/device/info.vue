<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="设备详情">
    <template #extra>
      <div class="flex items-center gap-4">
        <a-button type="primary">解绑定终端客户</a-button>
        <a-button type="primary">绑定终端客户</a-button>
        <a-button danger>解除关联合作伙伴</a-button>
        <a-button>关联合作伙伴</a-button>
      </div>
    </template>

    <div class="flex flex-col gap-4">
      <!-- 设备基础信息卡片 -->
      <a-card title="设备基础信息">
        <Description
          :data="apiResult"
          :schema="basicInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 设备详细信息卡片 -->
      <a-card title="设备详细信息">
        <Description
          :data="apiResult"
          :schema="detailInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 设备状态信息卡片 -->
      <DeviceStatusCard :status-info-schema="statusInfoSchema" />

      <!-- 连接配件信息卡片 -->
      <ConnectionAccessoriesCard :accessory-columns="accessoryColumns" />

      <!-- 远程维护卡片 -->
      <RemoteMaintenanceCard />

      <!-- 运程维护记录卡片 -->
      <MaintenanceRecordsCard :maintenance-columns="maintenanceColumns" />

      <!-- 关联合作伙伴记录内容 -->
      <PartnerRecordsCard :partner-columns="partnerColumns" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiGetDeviceInfoById } from '@/api/op/de';
  import { Description } from '@/components/Description';
  import {
    basicInfoSchema,
    detailInfoSchema,
    statusInfoSchema,
    accessoryColumns,
    maintenanceColumns,
    partnerColumns,
  } from './info.schema';

  import ConnectionAccessoriesCard from './components/ConnectionAccessoriesCard.vue';
  import RemoteMaintenanceCard from './components/RemoteMaintenanceCard.vue';
  import MaintenanceRecordsCard from './components/MaintenanceRecordsCard.vue';
  import PartnerRecordsCard from './components/PartnerRecordsCard.vue';
  import DeviceStatusCard from './components/DeviceStatusCard.vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;

  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => {
      if (params.id) {
        return await apiGetDeviceInfoById(params.id as string);
      }
      return {};
    },
    params,
  });
</script>

<style lang="less" scoped></style>
