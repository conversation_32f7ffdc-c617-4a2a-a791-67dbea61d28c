<template>
  <page-wrapper>
    <template #header>
      <ButtonSelect v-model="searchInfo.hasPartner" class="p-1.5" :options="statusOptions" />
    </template>

    <BasicTable @register="registerTable" @selection-change="handleSelectionChange">
      <template #tableTitle>
        <div class="flex items-center gap-4">
          <a-space>
            <a-button
              type="primary"
              @click="method.handleBatchBind"
              :disabled="!hasSelected"
              preIcon="ant-design:link-outlined"
            >
              批量绑定客户
            </a-button>
            <a-button
              type="primary"
              @click="method.handleExport"
              :loading="exportLoading"
              preIcon="ant-design:export-outlined"
            >
              导出
            </a-button>
          </a-space>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
        <template v-if="column.key === 'onlineStatus'">
          <a-badge
            :status="record.onlineStatus ? 'success' : 'default'"
            :text="record.onlineStatus ? '在线' : '离线'"
          />
        </template>
        <template v-if="column.key === 'partnerName'">
          <span v-if="record.partnerName">{{ record.partnerName }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import ButtonSelect from '@/components/Custom/ButtonSelect.vue';
  import { ref, computed } from 'vue';
  import { basicColumns, searchSchema } from '@/views/device/list.schema';
  import { message, Modal } from 'ant-design-vue';
  import { apiGetDeviceInfoPage, apiExportInventoryDevice } from '@/api/op/de';
  import { useApiExport } from '@/hooks/web/useApiExport';

  const go = useGo();

  // 使用 useApiExport 处理导出功能
  const { reload: exportData, loading: exportLoading } = useApiExport({
    api: apiExportInventoryDevice,
    fileName: '设备库存导出.xlsx',
  });

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  // 状态选项
  const statusOptions = [
    { value: null, label: '全部设备 (0)' },
    { value: true, label: '已关联合作伙伴 (0)' },
    { value: false, label: '未关联合作伙伴 (0)' },
  ];

  // 搜索信息
  const searchInfo = ref({
    hasPartner: null,
    queryType: 'INVENTORY_EQUIPMENT',
  });

  // 选中的行
  const selectedRowKeys = ref<string[]>([]);
  const hasSelected = computed(() => selectedRowKeys.value.length > 0);

  // 表格配置
  const [registerTable, { reload, getSearchInfo }] = useTable({
    api: apiGetDeviceInfoPage,
    rowKey: 'id',
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
    },
    searchInfo,
    useSearchForm: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {},
  });

  // 处理选择变化
  function handleSelectionChange({ keys }: { keys: string[] }) {
    selectedRowKeys.value = keys;
  }

  const method = {
    /** 批量绑定客户 */
    handleBatchBind() {
      if (!hasSelected.value) {
        message.warning('请先选择要绑定的设备');
        return;
      }
      Modal.info({
        title: '批量绑定客户',
        content: `已选择 ${selectedRowKeys.value.length} 台设备进行批量绑定`,
        onOk() {
          // 这里实现批量绑定逻辑
          message.success('批量绑定成功');
          selectedRowKeys.value = [];
          reload();
        },
      });
    },

    /** 导出 */
    async handleExport() {
      try {
        const searchInfo = getSearchInfo();
        await exportData(searchInfo);
        message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
      }
    },

    /** 详情 */
    detail: (record: Recordable) => {
      go(`/device/list/${record.id}`);
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: method.detail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
