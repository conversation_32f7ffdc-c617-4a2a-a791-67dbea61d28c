<template>
  <a-card>
    <BasicTable
      :columns="partnerColumns"
      :dataSource="partnerData"
      :pagination="partnerPagination"
      size="small"
    >
      <template #tableTitle>
        <ButtonSelect
          v-model:value="activeTabKey"
          :options="tabOptions"
          @change="handleTabChange"
        />
      </template>
    </BasicTable>
  </a-card>
</template>

<script lang="ts" setup>
  import { BasicTable } from '@/components/Table';
  import ButtonSelect from '@/components/Custom/ButtonSelect.vue';
  import { ref } from 'vue';

  // 定义props接收外部传入的columns
  interface Props {
    partnerColumns?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    partnerColumns: () => [],
  });

  // 标签页状态
  const activeTabKey = ref('maintenance');

  // 标签页选项
  const tabOptions = ref([
    { value: 'maintenance', label: '运程维护记录' },
    { value: 'partner', label: '关联合作伙伴记录' },
  ]);

  // 合作伙伴记录数据
  const partnerData = ref([
    {
      key: '1',
      time: '2023-12-16 08:30:00',
      operationType: '解除关联',
      partnerName: '名称1',
      reason: '其他',
      operator: '张三(zhangsan)',
    },
    {
      key: '2',
      time: '2023-12-16 08:30:00',
      operationType: '关联',
      partnerName: '名称1',
      reason: '其他',
      operator: '张三(zhangsan)',
    },
    {
      key: '3',
      time: '2023-12-16 08:30:00',
      operationType: '解除关联',
      partnerName: '名称1',
      reason: '其他',
      operator: '张三(zhangsan)',
    },
    {
      key: '4',
      time: '2023-12-16 08:30:00',
      operationType: '关联',
      partnerName: '名称1',
      reason: '其他',
      operator: '张三(zhangsan)',
    },
  ]);

  // 合作伙伴记录分页
  const partnerPagination = ref({
    current: 1,
    pageSize: 10,
    total: 4,
    showSizeChanger: true,
    showQuickJumper: true,
  });

  // 标签页切换处理
  const handleTabChange = (key: string | number | boolean | null | undefined) => {
    console.log('切换标签页:', key);
    // 这里可以添加标签页切换的逻辑
  };
</script>

<style lang="less" scoped></style>
