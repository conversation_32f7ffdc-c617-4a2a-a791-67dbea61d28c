<template>
  <a-card title="远程维护">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div
        v-for="item in remoteMaintenanceItems"
        :key="item.key"
        class="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
        @click="handleRemoteMaintenance(item.key)"
      >
        <Icon :icon="item.icon" :color="item.color" size="24" class="mb-2" />
        <span class="text-sm text-center">{{ item.label }}</span>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import Icon from '@/components/Icon/Icon.vue';
  import { ref } from 'vue';

  // 远程维护功能项
  const remoteMaintenanceItems = ref([
    {
      key: 'app_list',
      label: '应用列表',
      icon: 'ant-design:appstore-outlined',
      color: '#1890ff',
    },
    {
      key: 'query_device_info',
      label: '查询设备信息',
      icon: 'ant-design:info-circle-outlined',
      color: '#52c41a',
    },
    {
      key: 'sound_volume',
      label: '声音音量调节',
      icon: 'ant-design:sound-outlined',
      color: '#faad14',
    },
    {
      key: 'screenshot',
      label: '截屏',
      icon: 'ant-design:camera-outlined',
      color: '#f5222d',
    },
    {
      key: 'shutdown',
      label: '关机',
      icon: 'ant-design:poweroff-outlined',
      color: '#722ed1',
    },
    {
      key: 'restart',
      label: '恢复出厂',
      icon: 'ant-design:redo-outlined',
      color: '#13c2c2',
    },
    {
      key: 'reboot',
      label: '重启',
      icon: 'ant-design:reload-outlined',
      color: '#eb2f96',
    },
    {
      key: 'remote_operation',
      label: '远程操作',
      icon: 'ant-design:desktop-outlined',
      color: '#1890ff',
    },
  ]);

  // 远程维护操作处理
  const handleRemoteMaintenance = (key: string) => {
    console.log('远程维护操作:', key);
    // 这里可以添加具体的远程维护逻辑
  };
</script>

<style lang="less" scoped></style>
