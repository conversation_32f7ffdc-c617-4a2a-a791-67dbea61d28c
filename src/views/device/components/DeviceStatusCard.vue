<template>
  <a-card title="设备状态信息">
    <Description
      :data="deviceStatusInfo"
      :schema="statusInfoSchema"
      :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
      :bordered="false"
    />
  </a-card>
</template>

<script lang="ts" setup>
  import { Description } from '@/components/Description';
  import { computed } from 'vue';

  // 定义props接收外部传入的schema
  interface Props {
    statusInfoSchema?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    statusInfoSchema: () => [],
  });

  // 设备状态信息数据
  const deviceStatusInfo = computed(() => ({
    imsi: '-',
    iccid: '-',
    networkStatus: 'Wi-Fi',
    wifiName: 'TP-100',
    mobileOperator: '中国移动 4G',
    batteryLevel: 97,
    storage: '2.2/4GB',
    charging: '78%',
    mediaVolume: '78%',
    notificationVolume: '78%',
    isConnected: '未接入',
    cateringServiceStatus: '在线',
  }));
</script>

<style lang="less" scoped></style>
