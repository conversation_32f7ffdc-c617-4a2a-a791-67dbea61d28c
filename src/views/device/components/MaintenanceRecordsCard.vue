<template>
  <a-card title="运程维护记录">
    <BasicTable
      :columns="maintenanceColumns"
      :dataSource="maintenanceData"
      :pagination="maintenancePagination"
      size="small"
    />
  </a-card>
</template>

<script lang="ts" setup>
  import { BasicTable } from '@/components/Table';
  import { ref } from 'vue';

  // 定义props接收外部传入的columns
  interface Props {
    maintenanceColumns?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    maintenanceColumns: () => [],
  });

  // 维护记录数据
  const maintenanceData = ref([
    {
      key: '1',
      time: '2023-12-16 08:30:00',
      operationType: '远程控制',
      operationContent: '持续时间：00:08:12',
      operationIp: '*************',
      status: '成功',
      operator: '张三(zhangsan)',
    },
    {
      key: '2',
      time: '2023-12-16 08:30:00',
      operationType: '获取日志',
      operationContent: '执行获取日志',
      operationIp: '*************',
      status: '失败',
      operator: '张三(zhangsan)',
    },
    {
      key: '3',
      time: '2023-12-16 08:30:00',
      operationType: '调节音量',
      operationContent: '调节媒体音量至90%',
      operationIp: '*************',
      status: '成功',
      operator: '张三(zhangsan)',
    },
    {
      key: '4',
      time: '2023-12-16 08:30:00',
      operationType: '查询设备',
      operationContent: '执行查询设备',
      operationIp: '*************',
      status: '成功',
      operator: '张三(zhangsan)',
    },
    {
      key: '5',
      time: '2023-12-16 08:30:00',
      operationType: '查询设备',
      operationContent: '执行查询设备',
      operationIp: '*************',
      status: '成功',
      operator: '张三(zhangsan)',
    },
  ]);

  // 维护记录分页
  const maintenancePagination = ref({
    current: 1,
    pageSize: 5,
    total: 6,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`,
  });
</script>

<style lang="less" scoped></style>
