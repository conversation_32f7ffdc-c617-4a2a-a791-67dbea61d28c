<template>
  <a-card title="连接配件信息">
    <template #extra>
      <div class="flex items-center gap-2">
        <a-button size="small" type="text">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
        <a-button size="small" type="text">
          <template #icon>
            <SettingOutlined />
          </template>
        </a-button>
        <a-button size="small" type="text">
          <template #icon>
            <MoreOutlined />
          </template>
        </a-button>
      </div>
    </template>

    <BasicTable
      :columns="accessoryColumns"
      :dataSource="accessoryData"
      :pagination="false"
      size="small"
    />
  </a-card>
</template>

<script lang="ts" setup>
  import { BasicTable } from '@/components/Table';
  import { ReloadOutlined, SettingOutlined, MoreOutlined } from '@ant-design/icons-vue';
  import { ref } from 'vue';

  // 定义props接收外部传入的columns
  interface Props {
    accessoryColumns?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    accessoryColumns: () => [],
  });

  // 连接配件数据
  const accessoryData = ref([
    {
      key: '1',
      deviceType: '打印机',
      connectionStatus: '已连接',
      connectionMethod: '网口',
      deviceId: '-',
      deviceName: '-',
      ipAddress: '***********',
      macAddress: '-',
      bluetoothId: '-',
      uniqueId: '-',
      remarks: '后厨',
      status: '激活打印',
      operation: '其他操作',
    },
    {
      key: '2',
      deviceType: '打印机',
      connectionStatus: '未连接',
      connectionMethod: '无线',
      deviceId: '-',
      deviceName: '-',
      ipAddress: '***********',
      macAddress: '-',
      bluetoothId: '-',
      uniqueId: '-',
      remarks: '前台',
      status: '激活打印',
      operation: '其他操作',
    },
    {
      key: '3',
      deviceType: '打印机',
      connectionStatus: '已连接',
      connectionMethod: '蓝牙',
      deviceId: '-',
      deviceName: '-',
      ipAddress: '-',
      macAddress: '-',
      bluetoothId: "d'd'd'd",
      uniqueId: '-',
      remarks: '前台',
      status: '激活打印',
      operation: '其他操作',
    },
    {
      key: '4',
      deviceType: '打印机',
      connectionStatus: '已连接',
      connectionMethod: 'USB',
      deviceId: '-',
      deviceName: '-',
      ipAddress: '-',
      macAddress: '-',
      bluetoothId: '-',
      uniqueId: '-',
      remarks: '前台',
      status: '激活打印',
      operation: '其他操作',
    },
    {
      key: '5',
      deviceType: '钱箱',
      connectionStatus: '已连接',
      connectionMethod: '-',
      deviceId: '-',
      deviceName: '-',
      ipAddress: '-',
      macAddress: '-',
      bluetoothId: '-',
      uniqueId: '-',
      remarks: '',
      status: '打开钱箱',
      operation: '其他操作',
    },
  ]);
</script>

<style lang="less" scoped></style>
