<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" :dropDownActions="tableDropAction(record)" />
        </template>
        <template v-else-if="column.key === 'state'">
          <Switch
            size="small"
            :record="record"
            :checked="!!record.state"
            :api="method.changeStateApi"
          />
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { ref } from 'vue';
  import { apiGetCustomerPage, apiChangeCustomerState } from '@/api/op/customer';
  import { basicColumns, searchSchema } from './list.schema';
  import Switch from '@/components/Custom/Table/Switch.vue';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({
    status: 1,
    type: 'PARTNER',
  });

  const [registerTable, { reload }] = useTable({
    api: apiGetCustomerPage,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
      title: '伙伴列表',
    },

    searchInfo,
    useSearchForm: true,
    actionColumn: {
      width: 180,
    },
  });

  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/partner/list/${record.id}`);
    },
    /** 新增 */
    add: () => {
      go('/partner/form');
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      go(`/partner/form/${record.id}`);
    },
    /** 更新账号状态 */
    changeStateApi: async ({ value, record }: { value: boolean; record: any }) => {
      await apiChangeCustomerState(record.id, value);
      reload();
    },

    /** 删除 */
    del: async (record: Recordable) => {
      await apiChangeCustomerState(record.id, false);
      reload();
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: method.detail.bind(null, record),
      },
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '删除',
        onClick: method.del.bind(null, record),
      },
    ];
  }

  const tableDropAction = (record: Recordable): ActionItem[] => {
    return [
      {
        label: '禁用',
        onClick: method.changeStateApi.bind(null, {
          record,
          value: false,
        }),
        ifShow: record.state,
      },
      {
        label: '启用',
        onClick: method.changeStateApi.bind(null, {
          record,
          value: true,
        }),
        ifShow: !record.state,
      },
      {
        label: '重置密码',
        onClick: method.changeStateApi.bind(null, {
          record,
          value: false,
        }),
      },
    ];
  };
</script>

<style lang="less" scoped></style>
