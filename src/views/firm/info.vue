<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="详情">
    <template #header>
      <BasicUser
        :username="get(apiResult, 'name')"
        :description="get(apiResult, 'version')"
        showDes
      >
        <template #extra>
          <a-button type="primary">添加升级任务</a-button>
        </template>
      </BasicUser>
    </template>
    <Card :tab-list="tabList" :active-tab-key="tabActiveKey">
      <!-- 这里组件   -->
    </Card>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { Description } from '@/components/Description';
  import { get } from 'lodash-es';
  import { Card } from 'ant-design-vue';
  import { ref } from 'vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const tabList = [];
  const tabActiveKey = ref('');
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => ({}),
    params,
  });
</script>

<style lang="less" scoped></style>
