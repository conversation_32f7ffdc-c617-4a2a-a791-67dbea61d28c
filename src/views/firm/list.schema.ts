import { BasicColumn, FormSchema } from '@/components/Table';
import { apiInfoList } from '@/api/op/pr';

export const basicColumns: BasicColumn[] = [
  {
    title: '固件名称',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '固件版本',
    dataIndex: 'version',
    width: 120,
  },
  {
    title: '支持型号',
    dataIndex: 'productList',
    width: 200,
    customRender: ({ record }) => {
      if (record.productList && record.productList.length > 0) {
        return record.productList.map((product: any) => product.model).join(', ');
      }
      return '-';
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '固件描述',
    dataIndex: 'remark',
    width: 200,
    customRender: ({ text }) => text || '-',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'name',
    label: '固件名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入固件名称',
    },
  },
  {
    field: 'productId',
    label: '产品标识',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择产品标识',
      api: apiInfoList,
      labelField: 'model',
      valueField: 'id',
    },
  },
  {
    field: '[beginTime, endTime]',
    label: '创建时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期起始', '结束日期截止'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'productIds',
    label: '支持产品',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      placeholder: '请选择产品标识',
      api: apiInfoList,
      labelField: 'model',
      valueField: 'id',
      mode: 'multiple',
    },
  },
  {
    field: 'name',
    label: '固件名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入固件名称',
    },
  },
  {
    field: 'version',
    label: '固件版本',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入固件版本',
    },
  },

  {
    field: 'file',
    label: '固件文件',
    component: 'UploadFile',
    required: true,
    componentProps: {},
  },
  {
    field: 'signType',
    label: '签名方式',
    component: 'Select',
    defaultValue: 'MD5',
    required: true,
    componentProps: {
      options: [{ label: 'MD5', value: 'MD5' }],
    },
  },
  {
    field: 'sign',
    label: '签名值',
    required: true,
    component: 'Input',
    componentProps: {
      placeholder: '请输入签名值',
    },
  },
  {
    field: 'remark',
    label: '固件描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入固件描述',
      rows: 4,
    },
  },
];
