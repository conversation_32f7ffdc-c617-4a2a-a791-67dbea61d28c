<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          添加固件
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { message } from 'ant-design-vue';
  import {
    apiFirmwareStorePageInfo,
    apiAddFirmwareStore,
    apiUpdateFirmwareStore,
    apiDeleteFirmwareStore,
  } from '@/api/op/firm';
  import { basicColumns, searchSchema, formSchema } from './list.schema';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({});

  const [registerTable, { reload }] = useTable({
    api: apiFirmwareStorePageInfo,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
      title: '固件升级',
    },
    searchInfo,
    useSearchForm: true,
    actionColumn: {
      width: 180,
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: formSchema,
    addFn: apiAddFirmwareStore,
    updateFn: apiUpdateFirmwareStore,
    merge: (values: any) => {
      // 处理产品ID数组转换为字符串
      if (values.productIds && Array.isArray(values.productIds)) {
        values.productIds = values.productIds.join(',');
      }
      return values;
    },
  });
  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/firm/list/${record.id}`);
    },
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      // 处理产品ID字符串转换为数组
      const updateRecord = { ...record };
      if (updateRecord.productIds && typeof updateRecord.productIds === 'string') {
        updateRecord.productIds = updateRecord.productIds.split(',').map((id) => id.trim());
      }
      updateDrawer({
        record: updateRecord,
      });
    },
    /** 删除 */
    delete: async (record: Recordable) => {
      try {
        await apiDeleteFirmwareStore(record.id);
        message.success('删除成功');
        reload();
      } catch (error) {
        message.error('删除失败');
      }
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.detail.bind(null, record),
      },
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除此固件吗？',
          confirm: method.delete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
