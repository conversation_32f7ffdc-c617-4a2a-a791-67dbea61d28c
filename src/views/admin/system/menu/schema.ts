import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import Icon from '@/components/Icon/Icon.vue';
import { apiGetMenuTree } from '@/api/admin/menu';
import { get, isEmpty } from 'lodash-es';
import { MenuTypeArray, IsShowStrArray, IsEnableBooleanArray } from '@/maps/sysMaps';
import { showToBadge, showToTag, showToText } from '@/components/RenderVnode';

export const columns: BasicColumn[] = [
  {
    title: '菜单名称',
    dataIndex: 'name',
    resizable: true,
  },
  {
    title: '图标',
    width: 80,
    dataIndex: 'icon',
    customRender: ({ record }) => {
      const icon = get(record, 'meta.icon');
      if (icon) {
        return h(Icon, { icon });
      }
    },
  },
  {
    title: 'i18n编码',
    dataIndex: 'code',
  },
  {
    title: '组件地址',
    dataIndex: 'path',
  },
  {
    title: '权限标识',
    dataIndex: 'permission',
  },
  {
    title: '类型',
    dataIndex: 'menuType',
    customRender: ({ text }) => {
      return showToTag({ text, arr: MenuTypeArray });
    },
  },
  {
    title: '缓冲',
    dataIndex: 'keepAlive',
    customRender: ({ record }) => {
      return showToBadge({ text: get(record, 'meta.isKeepAlive'), arr: IsEnableBooleanArray });
    },
  },
  {
    title: '显示',
    dataIndex: 'isHide',
    customRender: ({ record }) => {
      return showToBadge({ text: !get(record, 'meta.isHide'), arr: IsEnableBooleanArray });
    },
  },
  {
    title: '排序',
    sorter: true,
    dataIndex: 'sortOrder',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '菜单名称',
    field: 'menuName',
    component: 'Input',
  },
];

export const baseSchema: FormSchema[] = [
  {
    field: 'menuType',
    label: '类型',
    component: 'RadioButtonGroup',
    fields: ['menuId', 'code'],
    componentProps: {
      options: MenuTypeArray,
    },
    defaultValue: '0',
    helpMessage: '菜单类型为按钮时，将不会显示在菜单栏中',
  },
  {
    field: 'parentId',
    label: '上级菜单',
    component: 'ApiTreeSelect',
    defaultValue: '-1',
    componentProps: {
      api: async () => {
        try {
          const tree = formartMenuData(
            await apiGetMenuTree({
              type: 0,
            }),
          );
          tree.unshift({ id: '-1', name: '根菜单' });
          return Promise.resolve(tree);
        } catch (error) {
          return Promise.reject(error);
        }
      },
      valueField: 'id',
      labelField: 'name',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'name',
    label: ({ model }) => {
      return showToText({ text: model.menuType, arr: MenuTypeArray }) + '名称';
    },
    component: 'Input',
    required: true,
  },
  {
    field: 'permission',
    label: '权限标识',
    component: 'Input',
  },
  {
    field: 'path',
    label: '路由',
    component: 'Input',
    componentProps: {
      maxlength: 100,
    },
    required: true,
    ifShow: ({ model }) => {
      return model.menuType !== '1';
    },
  },
  {
    field: 'component',
    label: '组件地址',
    help: '组件地址为空时，将使用路由地址',
    component: 'Input',
    componentProps: {
      maxlength: 100,
    },
    ifShow: ({ model }) => {
      return model.menuType !== '1';
    },
  },

  {
    field: 'icon',
    label: '菜单图标',
    component: 'IconPicker',
    ifShow: ({ model }) => {
      return model.menuType !== '1';
    },
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    defaultValue: 0,
    required: true,
    componentProps: {
      min: 0,
      step: 1,
      precision: 0,
    },
  },
  {
    field: 'keepAlive',
    label: '缓冲',
    colProps: {
      span: 12,
    },
    component: 'RadioGroup',
    componentProps: {
      options: IsShowStrArray,
    },
    defaultValue: '0',
  },
  {
    field: 'visible',
    label: '是否显示',
    colProps: {
      span: 12,
    },
    component: 'RadioGroup',
    componentProps: {
      options: IsShowStrArray,
    },
    defaultValue: '1',
  },
];

function formartMenuData(data: any) {
  const res: any = [];
  if (!data) return res;
  data.forEach((item: any) => {
    const tmp: any = { ...item };

    if (item.children) {
      const _res = formartMenuData(item.children);
      if (_res?.length > 0) {
        tmp.children = _res;
      }
    }
    if (!isEmpty(tmp)) {
      res.push(tmp);
    }
  });
  return res;
}
