import { DescItem } from '@/components/Description';
import { statusBadge } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';
import { get } from 'lodash-es';
import { apiTypeTreeGet, apiInfoList } from '@/api/op/pr';

// 基本信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'companyName',
    label: '客户公司全称',
  },
  {
    field: 'companyShort',
    label: '客户简称',
  },
  {
    field: 'linkman',
    label: '联系人',
  },
  {
    field: 'mobile',
    label: '联系电话',
  },
  {
    field: 'email',
    label: '邮箱',
  },
  {
    field: 'location',
    label: '所在地区',
  },
  {
    field: 'address',
    label: '详细地址',
  },
];

export const otherInfoSchema: DescItem[] = [
  {
    field: 'taxNumber',
    label: '公司税号',
  },
  {
    field: 'bankCode',
    label: '开户行',
  },
  {
    field: 'corporateAccount',
    label: '对公账户',
  },
  {
    field: 'introduction',
    label: '公司介绍',
  },
  {
    field: 'username',
    label: '登录账号',
  },
  {
    field: 'remark',
    label: '备注',
  },
];

// 表格列
export const basicColumns: BasicColumn[] = [
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '序列号',
    dataIndex: 'deviceSn',
  },
  {
    title: '状态',
    dataIndex: 'dmsDeviceState',
    customRender: ({ record }) => {
      return statusBadge({
        text: get(record, 'dmsDeviceState.oline'),
        arr: [
          { value: true, label: '在线', color: 'green' },
          { value: false, label: '离线', color: 'red' },
        ],
      });
    },
  },
  {
    title: '绑定客户原因',
    dataIndex: 'customerBindRecord.reasonDesc',
    customRender: ({ record }) => {
      return get(record, 'customerBindRecord.reasonDesc') || '--';
    },
  },
  {
    title: '时间',
    dataIndex: 'customerBindRecord.createTime',
    customRender: ({ record }) => {
      return get(record, 'customerBindRecord.createTime') || '--';
    },
  },
];

// 表格搜索
export const basicSearchSchema: FormSchema[] = [
  {
    field: '_categoryId',
    label: '选择分类',
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => {
      return {
        api: apiTypeTreeGet,
        valueField: 'id',
        labelField: 'name',
        onChange: () => {
          if (formModel?.model) {
            formModel.model = undefined;
          }
        },
      };
    },
  },
  {
    field: 'model',
    label: '选择型号',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: (params) => apiInfoList(params),
        labelField: 'model',
        valueField: 'model',
        params: {
          typeId: formModel?._categoryId,
        },
      };
    },
  },
  {
    field: 'deviceSN',
    label: '输入序列号',
    component: 'Input',
  },
];
