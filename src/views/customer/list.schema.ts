import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '客户简称',
    dataIndex: 'companyShort',
  },
  {
    title: '地区',
    dataIndex: 'location',
  },
  {
    title: '联系人',
    dataIndex: 'linkman',
  },
  {
    title: '联系电话',
    dataIndex: 'mobile',
  },
  {
    title: '绑定数量',
    dataIndex: 'deviceCount',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'name',
    label: '客户名称',
    component: 'Input',
  },
  {
    field: '[_,province]',
    label: '省',
    component: 'RegionSelect',
    componentProps: {
      level: 2,
    },
  },
];
