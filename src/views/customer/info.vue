<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="详情">
    <div class="flex flex-col gap-4">
      <a-card>
        <a-row>
          <a-col :span="8">
            <div class="flex flex-col items-center justify-center gap-[10px]">
              <Avatar :size="80" />
              <span class="text-[20px] font-bold mt-1">{{ apiResult?.linkman }}</span>
              <div class="flex gap-[2px] text-secondary">
                <span>地区：</span>
                <span>{{ apiResult?.location }}</span>
              </div>
            </div>
          </a-col>
          <a-col :span="16">
            <BasicTitle block>基础信息</BasicTitle>
            <Description
              :data="apiResult"
              :schema="basicInfoSchema"
              :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
              :bordered="false"
            />
            <BasicTitle block>其他信息</BasicTitle>
            <Description
              :data="apiResult"
              :schema="otherInfoSchema"
              :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
              :bordered="false"
            />
          </a-col>
        </a-row>
      </a-card>

      <a-card>
        <template #title>
          <div class="flex items-center gap-[12px]">
            <BasicTitle>关联设备清单</BasicTitle>
            <div class="text-secondary">总数：{{ total }}</div>
          </div>
        </template>
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ACTION'">
              <TableAction :actions="tableAction(record)" />
            </template>
          </template>
        </BasicTable>
      </a-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiGetCustomerById } from '@/api/op/customer';
  import { Description } from '@/components/Description';
  import { basicInfoSchema, otherInfoSchema, basicColumns, basicSearchSchema } from './info.schema';
  import { Avatar } from 'ant-design-vue';
  import BasicTitle from '@/components/Custom/BasicTitle.vue';
  import { TableAction, BasicTable, useTable, ActionItem } from '@/components/Table';
  import { computed, ref } from 'vue';
  import { apiGetDeviceInfoPage } from '@/api/op/de';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const { loading, apiResult } = useApiLoading({
    api: async (params: any) => apiGetCustomerById(params.id),
    params,
  });

  // 总数
  const total = ref(0);

  const [registerTable] = useTable({
    formConfig: {
      schemas: basicSearchSchema,
    },

    api: async (params) => {
      const result = await apiGetDeviceInfoPage(params);
      total.value = result?.total || 0;
      return result;
    },
    actionColumn: {},
    searchInfo: computed(() => {
      return {
        customerId: params.id,
        queryType: 'CUSTOMER_EQUIPMENT',
      };
    }),
    columns: basicColumns,
    // 是否立即请求
    immediate: true,
    useSearchForm: true,
  });
  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/device/info/${record.id}`);
    },
  };
  /** 表格操作列 */
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '查看详情',
        onClick: method.detail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
