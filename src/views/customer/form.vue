<template>
  <page-wrapper contentBackground v-loading="loading" @back="go(-1)" title="新增编辑">
    <div class="p-[35px]">
      <BasicForm @submit="methods.onSubmit" @register="formRegister" />
    </div>
    <template #leftFooter>
      <ButtonAction :loading="submitLoading" @submit="submit" @cancel="() => go(-1)" />
    </template>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { BasicForm, useForm } from '@/components/Form';
  import ButtonAction from '@/components/Custom/ButtonAction.vue';
  import { DEFAULT_FORM_COL_CONFIG } from '@/components/Form/src/const';
  import { unref, computed, onMounted } from 'vue';
  import { basicSchema } from './form.schema';
  import { apiAddCustomer, apiUpdateCustomer, apiGetCustomerById } from '@/api/op/customer';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const isEdit = computed(() => {
    return !!params.id;
  });

  const { reload: recordInfo, loading } = useApiLoading({
    api: (params: any) => apiGetCustomerById(params.id),
    params,
    immediate: false,
  });

  const { reload, loading: submitLoading } = useApiLoading({
    api: (_params) => {
      return unref(isEdit) ? apiUpdateCustomer(_params) : apiAddCustomer(_params);
    },
    immediate: false,
  });

  const [formRegister, { submit, setFieldsValue }] = useForm({
    schemas: basicSchema,
    ...DEFAULT_FORM_COL_CONFIG,
    mergeData: {
      type: 'CUSTOMER',
    },
  });

  const methods = {
    init() {
      if (isEdit.value) {
        // 这是一个编辑的
        recordInfo().then((res) => {
          setFieldsValue(res || {});
        });
      }
    },
    // businessCycle
    // 提交用户信息
    onSubmit: async (value) => {
      console.log('提交表单', value);
      reload(value).then(() => {
        go(-1);
      });
    },
  };

  onMounted(() => {
    methods.init();
  });
</script>

<style lang="less" scoped>
  // 解决表单组件高度不一致的问题
  :deep(.ant-row) {
    // 确保行内的列都从顶部开始对齐
    align-items: flex-start !important;

    .ant-col {
      // 确保每列的内容都从顶部开始
      align-self: flex-start;
    }
  }

  // 确保表单项的高度一致性
  :deep(.ant-form-item) {
    margin-bottom: 24px; // 统一表单项间距

    .ant-form-item-control-input {
      min-height: 32px; // 设置最小高度，保持一致性
    }
  }

  // 特别处理上传图片组件
  :deep(.ant-form-item-control-input-content) {
    // 上传组件容器
    .upload-container {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
  }

  // 确保左右两列的间距一致
  :deep(.ant-form) .ant-row {
    margin-right: -10px !important;
    margin-left: -10px !important;

    .ant-col {
      padding-right: 10px !important;
      padding-left: 10px !important;
    }
  }
</style>
