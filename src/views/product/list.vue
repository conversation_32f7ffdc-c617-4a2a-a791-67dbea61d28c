<template>
  <page-wrapper>
    <SidebarLayout :defaultWidth="220" fillBackground>
      <template #left-title>
        <a-button
          preIcon="ant-design:plus-outlined"
          type="primary"
          size="small"
          @click="method.addCategory"
        >
          添加分类
        </a-button>
      </template>
      <template #left>
        <BasicSelectTree
          :treeData="treeData"
          :loading="loading"
          v-model:selectedKey="activeKey"
          labelField="name"
          valueField="id"
        >
          <template #title="title">
            <BasicSelectTreeItem
              :hidden-actions="title.record?.id === -1"
              v-bind="title"
              :actions="treeAction(title.record)"
            />
          </template>
        </BasicSelectTree>
      </template>
      <template #right>
        <BasicTable v-if="activeKey" @register="registerTable">
          <template #tableTitle>
            <a-space>
              <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
                新增
              </a-button>
              <a-button type="primary" @click="method.import" preIcon="ant-design:import-outlined">
                导入
              </a-button>
              <a-button
                :loading="exportLoading"
                type="primary"
                @click="method.export"
                preIcon="ant-design:export-outlined"
              >
                导出
              </a-button>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ACTION'">
              <TableAction :actions="tableAction(record)" />
            </template>
          </template>
        </BasicTable>
        <Empty v-else description="请选择左侧选项" />
      </template>
    </SidebarLayout>
    <SModalForm @register="registerCategoryForm" @success="reloadTreeData()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { computed, ref, unref } from 'vue';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import SidebarLayout from '@/components/Custom/SidebarLayout.vue';

  import {
    BasicSelectTree,
    BasicSelectTreeItem,
    type TreeActionItem,
    ImportFileFn,
  } from '@/components/Custom';
  import {
    apiTypeTreeGet,
    apiTypeAdd,
    apiTypeEdit,
    apiTypeDel,
    apiInfoPage,
    apiInfoDel,
    apiInfoTemplate,
    apiInfoImport,
    apiInfoExport,
  } from '@/api/op/pr';
  import { useApiLoading } from '@/hooks/web/useApiLoading';

  import { basicCategorySchema, basicColumns, searchSchema } from './list.schema';
  import { message, Modal, Empty } from 'ant-design-vue';
  import { omit } from 'lodash-es';
  import { useApiExport } from '@/hooks/web/useApiExport';

  const activeKey = ref();

  const go = useGo();

  const { reload: exportData, loading: exportLoading } = useApiExport({
    api: apiInfoExport,
    fileName: '产品信息',
  });

  const {
    loading,
    reload: reloadTreeData,
    apiResult,
  } = useApiLoading({
    api: apiTypeTreeGet,
  });

  const treeData = computed(() => {
    return [
      {
        id: -1,
        name: '全部',
        level: 0,
      },
      ...(unref(apiResult) || []),
    ];
  });

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const [registerTable, { reload, getSearchInfo }] = useTable({
    api: apiInfoPage,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema(),
    },
    beforeFetch: (params) => {
      if (params.typeId === -1) {
        return omit(params, ['typeId']);
      }
      return params;
    },
    searchInfo: {
      typeId: activeKey,
    },
    useSearchForm: true,
    actionColumn: {
      width: 140,
    },
  });

  const [registerCategoryForm, { addModal, updateModal }] = useSModalForm({
    schemas: basicCategorySchema,
    merge: (values: any) => {
      // 如果pid为 '0' 则表示顶级分类 需要删除
      if (values.pid === '0') {
        values.pid = null;
      }
      return values;
    },
    destroyOnClose: true,
    addFn: apiTypeAdd,
    updateFn: apiTypeEdit,
  });

  const method = {
    /**导出 */
    export: async () => {
      try {
        const searchInfo = getSearchInfo();
        // 过滤掉分页参数，只保留查询条件
        await exportData(searchInfo);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败');
      }
    },
    /** 导入 */
    import: () => {
      ImportFileFn({
        templateApi: apiInfoTemplate,
        importApi: (params, onUploadProgress) => {
          return apiInfoImport(params, { onUploadProgress });
        },
        onSuccess: () => {
          reload();
        },
      });
    },
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/product/info/${record.id}`);
    },
    /** 新增 */
    add: () => {
      go('/product/form');
    },
    /** 更新/编辑 */
    update: (record: Recordable) => {
      go(`/product/form/${record.id}`);
    },
    delete: async (record: Recordable) => {
      try {
        await apiInfoDel([record.id]);
      } finally {
        reload();
      }
    },
    /** 增加分类 */
    addCategory: async (record: Recordable) => {
      if (!record?.id) {
        addModal();
      }

      if (record?.level >= 1) {
        message.warning('最多只能添加二级分类');
        return;
      }

      addModal({
        record: {
          pid: record.id,
        },
      });
    },
    /** 编辑分类 */
    editCategory: async (record: Recordable) => {
      updateModal({
        record,
      });
    },
    /** 删除分类 */
    deleteCategory: async (record: Recordable) => {
      // 提示
      Modal.confirm({
        title: '删除分类',
        content: '确定删除该分类吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await apiTypeDel(record.id);
          } finally {
            reloadTreeData();
          }
        },
      });
    },
  };

  /** 表格操作列 */
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '查看',
        onClick: method.detail.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.delete.bind(null, record),
        },
      },
    ];
  }

  /** 树操作列 */
  function treeAction(record: Recordable): TreeActionItem[] {
    return [
      {
        label: '添加',
        onClick: method.addCategory.bind(null, record),
        ifShow: record.level < 1,
      },
      {
        label: '编辑',
        onClick: method.editCategory.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        onClick: method.deleteCategory.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
