import { DescItem } from '@/components/Description';
import { OperationSystemList } from '@/maps/prMaps';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { showToTag } from '@/components/RenderVnode';

// 产品基础信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'id',
    label: '产品ID',
  },
  {
    field: 'model',
    label: '产品名称',
  },
  {
    field: 'typeName',
    label: '产品分类',
  },
  {
    field: 'sourceName',
    label: '来源类型',
  },
  {
    field: 'createBy',
    label: '创建人',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
  {
    field: 'remark',
    label: '备注',
  },
];

// 产品设备数量信息
export const quantityInfoSchema: DescItem[] = [
  {
    field: 'stockCount',
    label: '产品库存总数',
  },
  {
    field: 'customerCount',
    label: '已绑定终端客户总数',
  },
];

// 产品更多信息
export const moreInfoSchema: DescItem[] = [
  {
    field: 'system',
    label: '操作系统',
    render: (val: string) => {
      // 使用 useMapWithI18n 来获取对应的系统名称
      const systemOptions = useMapWithI18n(OperationSystemList);
      return showToTag({
        text: val,
        arr: systemOptions,
      });
    },
  },
  {
    field: 'screensCount',
    label: '屏幕数量',
  },
  {
    field: 'mainScreen',
    label: '主屏分辨率',
  },
  {
    field: 'secondScreen',
    label: '副屏分辨率',
  },
  {
    field: 'mainScreenSize',
    label: '主屏尺寸',
  },
  {
    field: 'secondScreenSize',
    label: '副屏尺寸',
  },
];
