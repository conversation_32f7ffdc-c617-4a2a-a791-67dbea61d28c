<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="产品详情">
    <template #extra>
      <div class="flex items-center gap-4">
        <a-button type="primary" @click="method.editProduct">编辑产品</a-button>
        <a-button @click="method.deleteProduct" danger>删除产品</a-button>
      </div>
    </template>

    <div class="flex flex-col gap-4">
      <!-- 产品基础信息卡片 -->
      <a-card title="产品基础信息">
        <Description
          :data="apiResult"
          :schema="basicInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 产品设备数量信息卡片 -->
      <a-card title="产品设备数量信息">
        <Description
          :data="apiResult"
          :schema="quantityInfoSchema"
          :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 产品更多信息卡片 -->
      <a-card title="产品更多信息">
        <Description
          :data="apiResult"
          :schema="moreInfoSchema"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>

      <!-- 产品图册卡片 -->
      <a-card title="产品图片">
        <div class="product-images">
          <UploadImage
            v-model:value="productImages"
            :count="9"
            :disabled="loadingEdit"
            :file-type="['JPEG', 'JPG', 'PNG']"
            @change="method.updateProductImages"
            value-type="object"
          />
        </div>
      </a-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiInfoGet, apiInfoDel, apiInfoEdit } from '@/api/op/pr';
  import { Description } from '@/components/Description';
  import { basicInfoSchema, quantityInfoSchema, moreInfoSchema } from './info.schema';
  import { Modal } from 'ant-design-vue';
  import UploadImage from '@/components/Form/src/extend/UploadImage.vue';
  import { computed } from 'vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;

  // 产品图片
  const productImages = computed({
    get: () => apiResult.value?.fileResources,
    set: (val) => {
      apiResult.value.fileResources = val;
    },
  });

  const { loading, apiResult } = useApiLoading({
    api: (params) => apiInfoGet(params.id),
    params,
  });

  const { reload: reloadEdit, loading: loadingEdit } = useApiLoading({
    api: apiInfoEdit,
    immediate: false,
  });

  const method = {
    /** 编辑产品 */
    editProduct: () => {
      go(`/product/form/${params.id}`);
    },
    /** 删除产品 */
    deleteProduct: () => {
      Modal.confirm({
        title: '删除产品',
        content: '确认删除该产品吗？删除后不可恢复。',
        onOk: async () => {
          await apiInfoDel([params.id]);
          go(-1);
        },
      });
    },
    /** 产品图册更新 */
    updateProductImages: async (urls: object) => {
      await reloadEdit({
        id: params.id,
        fileResources: urls,
      });
    },
  };
</script>

<style lang="less" scoped>
  .product-images {
    padding: 16px 0;
  }
</style>
