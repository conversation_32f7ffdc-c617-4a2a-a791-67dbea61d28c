import { FormSchema } from '@/components/Form';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { SourceCodeList, OperationSystemList } from '@/maps/prMaps';
import { apiTypeTreeGet } from '@/api/op/pr';

// 独立表单页面的 schema（适用于独立的 form.vue 页面）
export const basicSchema: FormSchema[] = [
  {
    field: 'fileResources',
    fields: ['id'],
    label: '产品图片',
    component: 'UploadImage',
    componentProps: {
      valueType: 'object',
      count: 9,
    },
    colProps: { span: 24 },
  },
  {
    field: 'model',
    label: '产品型号',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'typeId',
    label: '产品分类',
    component: 'ApiTreeSelect',
    componentProps: {
      api: apiTypeTreeGet,
      valueField: 'id',
      labelField: 'name',
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'sourceCode',
    label: '来源类别',
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(SourceCodeList),
    },
    colProps: { span: 12 },
  },
  {
    field: 'system',
    label: '操作系统',
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(OperationSystemList),
    },
    colProps: { span: 12 },
  },
  {
    field: 'screensCount',
    label: '屏幕数量',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '2', value: 2 },
        { label: '1', value: 1 },
        { label: '0', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'mainScreen',
    label: '主屏分辨率',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'secondScreen',
    label: '副屏分辨率',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'mainScreenSize',
    label: '主屏幕尺寸',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'secondScreenSize',
    label: '副屏幕尺寸',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    colProps: { span: 24 },
  },
];
