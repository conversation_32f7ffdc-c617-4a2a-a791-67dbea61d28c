<template>
  <page-wrapper contentBackground v-loading="loading" @back="go(-1)" :title="pageTitle">
    <div class="p-[35px]">
      <BasicForm @submit="methods.onSubmit" @register="formRegister" />
    </div>
    <template #leftFooter>
      <ButtonAction :loading="submitLoading" @submit="submit" @cancel="() => go(-1)" />
    </template>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { BasicForm, useForm } from '@/components/Form';
  import ButtonAction from '@/components/Custom/ButtonAction.vue';
  import { DEFAULT_FROM_CONFIG } from '@/components/Form/src/const';
  import { unref, computed, onMounted } from 'vue';
  import { basicAppSchemaFn } from './form.schema';
  import { apiAddAppStore, apiUpdateAppStore, apiGetAppStoreDetail } from '@/api/op/app';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const isEdit = computed(() => {
    return !!params.id;
  });

  const pageTitle = computed(() => {
    return unref(isEdit) ? '编辑应用' : '新增应用';
  });

  const { reload: recordInfo, loading } = useApiLoading({
    api: (params: any) => apiGetAppStoreDetail(params.id),
    params,
    immediate: false,
  });

  const { reload, loading: submitLoading } = useApiLoading({
    api: (_params) => {
      console.log('提交参数', _params);
      return unref(isEdit) ? apiUpdateAppStore(_params) : apiAddAppStore(_params);
    },
    immediate: false,
  });

  const [formRegister, { submit, setFieldsValue }] = useForm({
    schemas: basicAppSchemaFn(),
    ...DEFAULT_FROM_CONFIG,
  });

  const methods = {
    init() {
      if (isEdit.value) {
        // 这是一个编辑的
        recordInfo().then((res) => {
          setFieldsValue(res || {});
        });
      }
    },
    // 提交应用信息
    onSubmit: async (value) => {
      console.log('提交表单', value);
      reload(value).then(() => {
        go(-1);
      });
    },
  };

  onMounted(() => {
    methods.init();
  });
</script>

<style lang="less" scoped></style>
