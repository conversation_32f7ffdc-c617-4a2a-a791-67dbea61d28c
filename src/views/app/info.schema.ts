import { DescItem } from '@/components/Description';
import { size } from 'lodash-es';
import { BasicColumn } from '@/components/Table';

// 基本信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'typeDesc',
    label: '分类',
  },
  {
    field: 'introduction',
    label: '简介',
  },
  {
    field: 'productList',
    label: '关联产品',
    render: (val: any) => {
      if (size(val) === 0) return '无';
      return val.map((item: any) => item.model).join(',');
    },
  },
];

export const otherInfoSchema: DescItem[] = [
  {
    field: 'developer',
    label: '开发者',
  },
  {
    field: 'createBy',
    label: '创建人',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
  {
    field: 'updateTime',
    label: '最后更新时间',
  },
];

// 更新记录列表
export const updateRecordColumns: BasicColumn[] = [
  {
    title: '时间',
    dataIndex: 'createTime',
  },
  {
    title: '版本号',
    dataIndex: 'versionCode',
  },
  {
    title: '操作人',
    dataIndex: 'createBy',
  },
  {
    title: '更新说明',
    dataIndex: 'remark',
  },
];
