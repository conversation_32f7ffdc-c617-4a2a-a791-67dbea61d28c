<template>
  <page-wrapper>
    <SidebarLayout :defaultWidth="220" fillBackground>
      <template #left-title>
        <a-button
          preIcon="ant-design:plus-outlined"
          type="primary"
          size="small"
          @click="method.addCategory"
        >
          添加分类
        </a-button>
      </template>
      <template #left>
        <BasicSelectTree
          :treeData="treeData"
          :loading="loading"
          v-model:selectedKey="activeKey"
          labelField="name"
          valueField="id"
        >
          <template #title="title">
            <BasicSelectTreeItem
              :hidden-actions="title.record?.id === -1"
              v-bind="title"
              :actions="treeAction(title.record)"
            />
          </template>
        </BasicSelectTree>
      </template>
      <template #right>
        <BasicTable
          v-if="activeKey"
          @register="registerTable"
          @selection-change="handleSelectionChange"
        >
          <template #tableTitle>
            <a-space>
              <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
                新增应用
              </a-button>
              <a-button
                :loading="exportLoading"
                type="primary"
                @click="method.export"
                preIcon="ant-design:export-outlined"
              >
                导出
              </a-button>
              <a-button
                :disabled="!hasSelected"
                @click="method.clearSelection"
                preIcon="ant-design:clear-outlined"
              >
                清空选择
              </a-button>
              <a-dropdown :disabled="!hasSelected" trigger="click">
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="batchOnShelf" @click="method.batchOnShelf">
                      <Icon icon="ant-design:arrow-up-outlined" />
                      批量上架
                    </a-menu-item>
                    <a-menu-item key="batchOffShelf" @click="method.batchOffShelf">
                      <Icon icon="ant-design:arrow-down-outlined" />
                      批量下架
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button postIcon="ant-design:down-outlined"> 批量操作 </a-button>
              </a-dropdown>
            </a-space>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ACTION'">
              <TableAction :actions="tableAction(record)" />
            </template>
          </template>
        </BasicTable>
        <a-empty v-else description="请选择左侧分类查看详情" />
      </template>
    </SidebarLayout>
    <SModalForm @register="registerCategoryForm" @success="reloadTreeData()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { computed, ref, unref } from 'vue';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import SidebarLayout from '@/components/Custom/SidebarLayout.vue';
  import { BasicSelectTree, BasicSelectTreeItem, type TreeActionItem } from '@/components/Custom';
  import Icon from '@/components/Icon/Icon.vue';
  import {
    apiGetAppTypeTree,
    apiAddAppType,
    apiUpdateAppType,
    apiDeleteAppType,
    apiGetAppStorePage,
    apiDeleteAppStore,
    apiExportAppStore,
    apiChangeAppStoreStatus,
  } from '@/api/op/app';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { basicCategorySchema, basicColumns, searchSchema } from './list.schema';
  import { message, Modal } from 'ant-design-vue';
  import { omit } from 'lodash-es';
  import { useApiExport } from '@/hooks/web/useApiExport';

  const activeKey = ref();

  const go = useGo();

  const { reload: exportData, loading: exportLoading } = useApiExport({
    api: apiExportAppStore,
    fileName: 'APP应用信息',
  });

  const {
    loading,
    reload: reloadTreeData,
    apiResult,
  } = useApiLoading({
    api: apiGetAppTypeTree,
  });

  const treeData = computed(() => {
    const result = unref(apiResult) || [];
    return [
      {
        id: -1,
        name: '全部',
        level: 0,
      },
      ...result,
    ];
  });

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  // 响应式的选中状态
  const selectedRowKeys = ref<any[]>([]);

  const [registerTable, { reload, getSearchInfo, getSelectRowKeys, clearSelectedRowKeys }] =
    useTable({
      api: apiGetAppStorePage,
      columns: basicColumns,
      formConfig: {
        schemas: searchSchema(),
      },
      beforeFetch: (params) => {
        if (params.typeId === -1) {
          return omit(params, ['typeId']);
        }
        return params;
      },
      searchInfo: {
        typeId: activeKey,
      },
      useSearchForm: true,
      actionColumn: {
        width: 140,
      },
      rowSelection: {
        type: 'checkbox',
      },
      showSelectionBar: false,
    });

  // 计算是否有选中的行
  const hasSelected = computed(() => {
    return selectedRowKeys.value && selectedRowKeys.value.length > 0;
  });

  // 监听表格选中状态变化
  function handleSelectionChange({ keys }: { keys: any[]; rows: any[] }) {
    selectedRowKeys.value = keys;
  }

  const [registerCategoryForm, { addModal, updateModal }] = useSModalForm({
    schemas: basicCategorySchema,
    merge: (values: any) => {
      // 如果pid为 '0' 则表示顶级分类 需要删除
      if (values.pid === '0') {
        values.pid = null;
      }
      return values;
    },
    destroyOnClose: true,
    addFn: apiAddAppType,
    updateFn: apiUpdateAppType,
  });

  const method = {
    /** 导出 */
    export: async () => {
      try {
        const searchInfo = getSearchInfo();
        // 过滤掉分页参数，只保留查询条件
        await exportData(searchInfo);
        message.success('导出成功');
      } catch (error) {
        message.error('导出失败');
      }
    },
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/app/list/${record.id}`);
    },
    /** 新增应用 */
    add: () => {
      go('/app/form');
    },
    /** 更新/编辑应用 */
    update: async (record: Recordable) => {
      go(`/app/form/${record.id}`);
    },
    /** 删除应用 */
    delete: async (record: Recordable) => {
      try {
        await apiDeleteAppStore([record.id]);
      } finally {
        reload();
      }
    },
    /** 切换应用状态 */
    changeStatus: async (record: Recordable, status: 'ON_SHELF' | 'OFF_SHELF') => {
      try {
        await apiChangeAppStoreStatus(status, [record.id]);
      } finally {
        reload();
      }
    },
    /** 上架应用 */
    onShelf: async (record: Recordable) => {
      await method.changeStatus(record, 'ON_SHELF');
    },
    /** 下架应用 */
    offShelf: async (record: Recordable) => {
      await method.changeStatus(record, 'OFF_SHELF');
    },
    /** 批量切换应用状态 */
    batchChangeStatus: async (status: 'ON_SHELF' | 'OFF_SHELF') => {
      const selectedKeys = getSelectRowKeys();
      const actionText = status === 'ON_SHELF' ? '上架' : '下架';

      if (!selectedKeys || selectedKeys.length === 0) {
        message.warning(`请先选择要${actionText}的应用`);
        return;
      }

      Modal.confirm({
        title: `批量${actionText}`,
        content: `确定要${actionText}选中的 ${selectedKeys.length} 个应用吗？`,
        onOk: async () => {
          try {
            await apiChangeAppStoreStatus(status, selectedKeys as number[]);
            clearSelectedRowKeys();
            selectedRowKeys.value = []; // 清空响应式选中状态
          } finally {
            reload();
          }
        },
      });
    },
    /** 批量上架 */
    batchOnShelf: async () => {
      await method.batchChangeStatus('ON_SHELF');
    },
    /** 批量下架 */
    batchOffShelf: async () => {
      await method.batchChangeStatus('OFF_SHELF');
    },
    /** 清空选择 */
    clearSelection: () => {
      clearSelectedRowKeys();
      selectedRowKeys.value = [];
      message.success('已清空选择');
    },
    /** 新增分类 */
    addCategory: (record: Recordable) => {
      if (!record?.id) {
        addModal();
      }
      addModal({
        record: {
          pid: record.id,
        },
      });
    },
    /** 编辑分类 */
    editCategory: async (record: Recordable) => {
      updateModal({
        record,
      });
    },
    /** 删除分类 */
    deleteCategory: async (record: Recordable) => {
      Modal.confirm({
        title: '删除分类',
        content: '确定删除该分类吗？',
        onOk: async () => {
          try {
            await apiDeleteAppType(record.id);
          } finally {
            reloadTreeData();
          }
        },
      });
    },
  };

  /** 表格操作列 */
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '查看',
        onClick: method.detail.bind(null, record),
      },
      {
        label: '上架',
        onClick: method.onShelf.bind(null, record),
        ifShow: record.status !== 'ON_SHELF',
      },
      {
        label: '下架',
        onClick: method.offShelf.bind(null, record),
        ifShow: record.status === 'ON_SHELF',
      },
      {
        label: '删除',
        danger: true,
        ifShow: record.status !== 'ON_SHELF',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.delete.bind(null, record),
        },
      },
    ];
  }

  /** 树操作列 */
  function treeAction(record: Recordable): TreeActionItem[] {
    return [
      {
        label: '添加',
        onClick: method.addCategory.bind(null, record),
        ifShow: record.level < 1,
      },
      {
        label: '编辑',
        onClick: method.editCategory.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        onClick: method.deleteCategory.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
