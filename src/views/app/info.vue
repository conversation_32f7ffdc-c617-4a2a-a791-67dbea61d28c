<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="详情">
    <div class="flex flex-col gap-4">
      <a-card>
        <a-row>
          <a-col :span="8">
            <div class="flex flex-col items-center justify-center gap-[10px]">
              <Avatar :src="get(apiResult, 'logo')" :size="80" />
              <span class="text-[20px] font-bold mt-1">{{ get(apiResult, 'name') }}</span>
              <div class="flex gap-[2px] text-secondary">
                <span>平台：</span>
                <span>{{ getLabel(get(apiResult, 'platform')) }}</span>
              </div>
              <div class="flex gap-[2px] text-secondary">
                <span>版本：</span>
                <span>{{ get(apiResult, 'versionCode') }}</span>
              </div>
            </div>
          </a-col>
          <a-col :span="16">
            <BasicTitle block>基础信息</BasicTitle>
            <Description
              :data="apiResult"
              :schema="basicInfoSchema"
              :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
              :bordered="false"
            />
            <BasicTitle block>其他信息</BasicTitle>
            <Description
              :data="apiResult"
              :schema="otherInfoSchema"
              :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
              :bordered="false"
            />
          </a-col>
        </a-row>
      </a-card>
      <a-card title="应用备注信息">
        {{ get(apiResult, 'remark') }}
      </a-card>
      <a-card title="应用截图">
        <UploadImage preview-mode :value="previewImage" value-type="object" />
      </a-card>
      <a-card title="更新记录">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ACTION'">
              <TableAction :actions="tableAction(record)" />
            </template>
          </template>
        </BasicTable>
      </a-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicTitle from '@/components/Custom/BasicTitle.vue';
  import { Description } from '@/components/Description';
  import { basicInfoSchema, otherInfoSchema, updateRecordColumns } from './info.schema';
  import { Avatar, message } from 'ant-design-vue';
  import { get } from 'lodash-es';
  import { apiGetAppStoreDetail, apiGetAppVersionPage } from '@/api/op/app';
  import { useMapLabel } from '@/hooks/web/useMapLabel';
  import { AppPublishPlatformList } from '@/maps/prMaps';
  import UploadImage from '@/components/Form/src/extend/UploadImage.vue';
  import { TableAction, BasicTable, useTable, ActionItem } from '@/components/Table';
  import { computed } from 'vue';
  import { downloadByUrl } from '@/utils/file/download';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async (params: any) => {
      return apiGetAppStoreDetail(params.id);
    },
    params,
  });

  const [registerTable] = useTable({
    api: apiGetAppVersionPage,
    columns: updateRecordColumns,
    useSearchForm: false,
    searchInfo: computed(() => {
      return {
        appId: get(params, 'id'),
      };
    }),
    actionColumn: {},
  });

  // 图片预览
  const previewImage = computed<any[]>(() => {
    return get(apiResult.value, 'resourcesList', []) || [];
  });

  // 使用映射标签转换（支持国际化）
  const { getLabel } = useMapLabel(AppPublishPlatformList);

  /** 下载应用文件 */
  function handleDownload(record: Recordable) {
    const downloadUrl = get(record, 'apk');
    const fileName = `${get(record, 'name', 'app')}_v${get(record, 'versionCode', '1.0')}.apk`;

    if (!downloadUrl) {
      message.error('下载链接不存在');
      return;
    }

    try {
      // 使用 downloadByUrl 进行下载
      const success = downloadByUrl({
        url: downloadUrl,
        fileName: fileName,
        target: '_blank',
      });

      if (!success) {
        message.error('下载失败，请检查浏览器设置');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败');
    }
  }

  /** 表格操作列 */
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '下载',
        onClick: () => handleDownload(record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
