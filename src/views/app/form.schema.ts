import { FormSchema } from '@/components/Table';
import { apiGetAppTypeTree } from '@/api/op/app';
import { rule } from '@/utils/validate';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { AppPublishPlatformList } from '@/maps/prMaps';
import { apiInfoList } from '@/api/op/pr';

// 独立表单页面的应用 schema（适用于独立的 form.vue 页面）

export const basicAppSchemaFn = (): FormSchema[] => [
  {
    field: 'name',
    label: '应用名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入应用名称',
    },
    colProps: { span: 12 },
  },
  {
    field: 'introduction',
    label: '应用简介',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'platform',
    label: '平台',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: useMapWithI18n(AppPublishPlatformList),
    },
    rules: [{ required: true, message: '请选择平台', trigger: 'change' }],
    colProps: { span: 12 },
    defaultValue: 'android',
  },
  {
    field: 'typeId',
    label: '应用分类',
    fields: ['id'],
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: apiGetAppTypeTree,
      valueField: 'id',
      labelField: 'name',
      placeholder: '请选择应用分类',
    },
    rules: [{ required: true, message: '请选择应用分类', trigger: 'change' }],
    colProps: { span: 12 },
  },

  {
    field: 'productIds',
    label: '产品标识',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      placeholder: '请输入产品标识',
      api: apiInfoList,
      labelField: 'model',
      valueField: 'id',
    },
    colProps: { span: 12 },
  },
  {
    field: 'logo',
    label: '应用LOGO',
    component: 'UploadImage',
    required: true,
    componentProps: {
      count: 1,
      valueType: 'string',
    },
    rules: [{ required: true, message: '请上传应用LOGO', trigger: 'change' }],
    colProps: { span: 24 },
  },
  {
    field: 'resourcesList',
    label: '应用截图',
    component: 'UploadImage',
    componentProps: {
      count: 9,
      valueType: 'object',
    },
    colProps: { span: 24 },
  },
  {
    field: 'apk',
    label: '应用包',
    component: 'UploadFile',
    required: true,
    componentProps: {
      count: 1,
      valueType: 'string',
    },
    colProps: { span: 24 },
  },
  {
    field: 'appVersion',
    label: '应用版本',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入应用版本',
    },
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '应用版本不能为空', trigger: 'blur' },
    ],
    colProps: { span: 12 },
  },
  {
    field: 'versionCode',
    label: '版本号',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      placeholder: '请输入版本号',
    },
    rules: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
    colProps: { span: 12 },
  },
  {
    field: 'packageName',
    label: '包名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入包名',
    },
    colProps: { span: 12 },
  },
  {
    field: 'developer',
    label: '开发者',
    component: 'Input',
    componentProps: {
      placeholder: '请输入开发者',
    },
    colProps: { span: 12 },
  },

  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 3,
    },
    colProps: { span: 24 },
  },
];
