import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const product: AppRouteModule = {
  path: '/product',
  name: 'Product',
  component: LAYOUT,
  redirect: '/product/list',
  meta: {
    orderNo: 20,
    icon: 'ant-design:product-outlined',
    title: '产品管理',
  },
  children: [
    {
      path: 'list',
      name: 'ProductList',
      component: () => import('@/views/product/list.vue'),
      meta: {
        title: '产品列表',
        icon: 'ant-design:unordered-list-outlined',
      },
    },
    {
      path: 'form/:id?',
      name: 'ProductForm',
      component: () => import('@/views/product/form.vue'),
      meta: {
        title: '产品表单',
        hideMenu: true,
        currentActiveMenu: '/product/list',
      },
    },
    {
      path: 'info/:id',
      name: 'ProductInfo',
      component: () => import('@/views/product/info.vue'),
      meta: {
        title: '产品详情',
        hideMenu: true,
        currentActiveMenu: '/product/list',
      },
    },
  ],
};

export default product;
