import { defHttp } from '@/utils/http/axios';
import type { AxiosRequestConfig } from 'axios';
import type { UploadFileParams } from '#/axios';
import { useGlobSetting } from '@/hooks/setting';

const { apiUrl, ossUrl } = useGlobSetting();
enum Api {
  SysFile = '/admin/sys-file',
  SysFilePage = '/admin/sys-file/page',
  SysFileGroup = '/admin/sys-file/group',
  SysFileGroupAdd = '/admin/sys-file/group/add',
  SysFileGroupUpdate = '/admin/sys-file/group/update',
  SysFileGroupDelete = '/admin/sys-file/group/delete',
  SysFileGroupList = '/admin/sys-file/group/list',
  SysFileGroupMove = '/admin/sys-file/group/move',
  SysFileRename = '/admin/sys-file/rename',
  SysFileUpload = '/admin/sys-file/upload',
  SysFileGetUploadUrl = '/admin/sys-file/getUploadUrl',
}

/**
 * 获取文件分页列表
 * @param params 查询参数
 * @returns 文件分页数据
 */
export const apiGetFileList = (params?: any) => {
  return defHttp.get({ url: Api.SysFilePage, params });
};

/**
 * 新增文件
 * @param data 文件数据
 * @returns 新增结果
 */
export const apiAddFile = (data?: any) => {
  return defHttp.post(
    { url: Api.SysFile, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 获取文件详情
 * @param id 文件ID
 * @returns 文件详情数据
 */
export const apiGetFile = (id: string) => {
  return defHttp.get({ url: `${Api.SysFile}/${id}` });
};

/**
 * 更新文件信息
 * @param data 文件数据
 * @returns 更新结果
 */
export const apiUpdateFile = (data?: any) => {
  return defHttp.put(
    { url: Api.SysFile, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 新增文件分组
 * @param data 分组数据
 * @returns 新增结果
 */
export const apiAddFileGroup = (data: Record<string, any>) => {
  return defHttp.post(
    { url: Api.SysFileGroupAdd, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 更新文件分组
 * @param data 分组数据
 * @returns 更新结果
 */
export const apiUpdateFileGroup = (data: Record<string, any>) => {
  return defHttp.put(
    { url: Api.SysFileGroupUpdate, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 删除文件分组
 * @param id 分组ID
 * @returns 删除结果
 */
export const apiDeleteFileGroup = (id: string | number) => {
  return defHttp.delete(
    { url: `${Api.SysFileGroupDelete}/${id}` },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 获取文件分组列表
 * @param params 查询参数
 * @returns 分组列表数据
 */
export const apiGetFileGroupList = (params?: Record<string, any>) => {
  return defHttp.get({ url: Api.SysFileGroupList, params });
};

/**
 * 删除文件
 * @param ids 文件ID数组
 * @returns 删除结果
 */
export const apiDeleteFile = (ids: string[]) => {
  return defHttp.delete(
    { url: Api.SysFile, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 移动文件到指定分组
 * @param data 移动参数（包含文件ID和目标分组ID）
 * @returns 移动结果
 */
export const apiMoveFile = (data: Record<string, any>) => {
  return defHttp.put(
    { url: Api.SysFileGroupMove, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 重命名文件
 * @param data 重命名参数（包含文件ID和新文件名）
 * @returns 重命名结果
 */
export const apiRenameFile = (data: { id: number; original: string }) => {
  return defHttp.put(
    { url: Api.SysFileRename, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 上传文件
 */
export const apiUploadFile = (params: UploadFileParams, config: AxiosRequestConfig = {}) => {
  return defHttp.uploadFile({ url: Api.SysFileUpload, baseURL: apiUrl, ...config }, params);
};

/**
 * 获取上传文件URL
 */
export const apiGetUploadUrl = (data: { objectName: string; contentType: string }) => {
  return defHttp.post<{
    objectName: string;
    url: string;
    fileUrl: string;
  }>({ url: Api.SysFileGetUploadUrl, data });
};

/**
 * 文件上传固定自己携带 baseUrl
 */
export const apiUploadFileWithBaseUrl = (
  params: UploadFileParams,
  config: AxiosRequestConfig = {},
) => {
  return defHttp.uploadFile({ ...config }, params);
};

/**
 * 使用PUT方式直接上传文件流到预签名URL
 * 主要用于对象存储服务(如OSS、S3等)的预签名URL上传场景
 * @param signedUrl 预签名的上传URL
 * @param file 要上传的文件
 * @param contentType 文件的Content-Type，可选
 * @param config 额外的axios配置
 */
export const apiUploadFileToSignedUrl = (
  signedUrl: string,
  file: File | Blob,
  contentType?: string,
  config: AxiosRequestConfig = {},
) => {
  // 根据环境变量处理 signedUrl
  let processedUrl = signedUrl;

  // 如果配置了 ossUrl 环境变量，且 signedUrl 包含域名，则进行替换
  if (ossUrl && (signedUrl.startsWith('https://') || signedUrl.startsWith('http://'))) {
    try {
      // 提取域名部分（包括协议）
      const urlObj = new URL(signedUrl);
      const domainWithProtocol = `${urlObj.protocol}//${urlObj.host}`;

      // 在开发/测试环境下，ossUrl 通常是代理路径（如 /ossxjp）
      // 在生产环境下，ossUrl 可能是完整的域名或代理路径
      processedUrl = signedUrl.replace(domainWithProtocol, ossUrl);
    } catch (error) {
      // 如果 URL 解析失败，使用原始 URL
      console.warn('Failed to parse signedUrl:', signedUrl, error);
      processedUrl = signedUrl;
    }
  }

  return defHttp.uploadFileStream(
    {
      url: '',
      baseURL: processedUrl, // 使用完整的预签名URL作为baseURL
      ...config,
    },
    {
      file,
      contentType,
    },
  );
};
