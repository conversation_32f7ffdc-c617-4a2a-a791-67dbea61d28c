import { defHttp } from '@/utils/http/axios';

enum Api {
  User = '/admin/user',
  UserPage = '/admin/user/page',
  UserList = '/admin/user/list',
  UserDetail = '/admin/user/details',
  UserPersonalEdit = '/admin/user/personal/edit',
  UserPersonalPassword = '/admin/user/personal/password',
  UserUnbinding = '/admin/user/unbinding',
  UserCheck = '/admin/user/check',
  RegisterUser = '/admin/register/user',
  RegisterPassword = '/admin/register/password',
  ResetPassword = '/admin/user/resetPassword',
}

export const apiUserList = (params: any) => defHttp.get({ url: Api.UserList, params });
export const apiUserPage = (params: any) => defHttp.get({ url: Api.UserPage, params });
export const apiUserDetail = (params: any) => defHttp.get({ url: Api.UserDetail, params });
export const apiUserDetailById = (id: string) => defHttp.get({ url: `${Api.UserDetail}/${id}` });
export const apiUserEdit = (data: any) => defHttp.put({ url: Api.User, data });
export const apiUserDelete = (id: string) => defHttp.delete({ url: `${Api.User}/${id}` });
export const apiUserAdd = (data: any) => defHttp.post({ url: Api.User, data });
export const apiUserPersonalEdit = (data: any) => defHttp.put({ url: Api.UserPersonalEdit, data });
export const apiUserPersonalPassword = (data: any) =>
  defHttp.put({ url: Api.UserPersonalPassword, data });

export const apiUserUnbinding = (type: any) =>
  defHttp.post({
    url: Api.UserUnbinding,
    params: {
      type,
    },
  });

export const apiUserCheckPassword = (password: string) =>
  defHttp.post({
    url: Api.UserCheck,
    params: {
      password,
    },
  });

export const apiRegisterUser = (data: any) => defHttp.post({ url: Api.RegisterUser, data });

export const apiRegisterPassword = (data: any) => defHttp.post({ url: Api.RegisterPassword, data });

export const validateUsername = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }

    apiUserDetail({ username: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('用户名已经存在'));
      } else {
        return resolve();
      }
    });
  });
};

export const validatePhone = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }
    apiUserDetail({ phone: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('手机号已经存在'));
      } else {
        return resolve();
      }
    });
  });
};

// 重置用户密码
export const apiResetPassword = (userId: string) =>
  defHttp.put(
    { url: `${Api.ResetPassword}/${userId}` },
    {
      successMessageMode: 'message',
    },
  );
