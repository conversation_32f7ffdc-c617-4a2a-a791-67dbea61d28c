import { defHttp } from '@/utils/http/axios';
import type {
  DmsAppType,
  AppTypePageParams,
  AppTypePageResult,
  AppTypeTreeResult,
  BooleanResult,
  DmsAppStore,
  AppStorePageParams,
  AppStorePageResult,
} from './model/appModel';

enum Api {
  AppType = '/admin/sys/app/type',
  AppTypeTree = '/admin/sys/app/type/tree',
  AppTypePageInfo = '/admin/sys/app/type/pageInfo',
  AppTypeDelete = '/admin/sys/app/type/{id}',
  // APP应用商城相关接口
  AppStore = '/admin/sys/app/store',
  AppStorePageInfo = '/admin/sys/app/store/pageInfo',
  AppStoreChange = '/admin/sys/app/store/change/{state}',
  AppStoreExport = '/admin/sys/app/store/export',
  AppStoreDelete = '/admin/sys/app/store/del',
  AppStoreDetail = '/admin/sys/app/store/info/{appId}',
  AppVersionPageInfo = '/admin/sys/app/version/pageInfo/{appId}',
}

/**
 * @description: 获取APP分类树形结构
 */
export const apiGetAppTypeTree = (params?: any): Promise<AppTypeTreeResult> => {
  return defHttp.get({ url: Api.AppTypeTree, params });
};

/**
 * @description: 获取APP分类分页列表
 */
export const apiGetAppTypePage = (params?: AppTypePageParams): Promise<AppTypePageResult> => {
  return defHttp.get({ url: Api.AppTypePageInfo, params });
};

/**
 * @description: 新增APP分类
 */
export const apiAddAppType = (data: DmsAppType): Promise<BooleanResult> => {
  return defHttp.post(
    { url: Api.AppType, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改APP分类
 */
export const apiUpdateAppType = (data: DmsAppType): Promise<BooleanResult> => {
  return defHttp.put(
    { url: Api.AppType, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除APP分类
 */
export const apiDeleteAppType = (id: string | number): Promise<BooleanResult> => {
  return defHttp.delete(
    { url: Api.AppTypeDelete.replace('{id}', String(id)) },
    {
      successMessageMode: 'message',
    },
  );
};

// ==================== APP应用商城相关接口 ====================

/**
 * @description: 获取APP应用商城分页列表
 */
export const apiGetAppStorePage = (params?: AppStorePageParams): Promise<AppStorePageResult> => {
  return defHttp.get({ url: Api.AppStorePageInfo, params });
};

/**
 * @description: 新增APP应用
 */
export const apiAddAppStore = (data: DmsAppStore): Promise<BooleanResult> => {
  return defHttp.post(
    { url: Api.AppStore, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改APP应用
 */
export const apiUpdateAppStore = (data: DmsAppStore): Promise<BooleanResult> => {
  return defHttp.put(
    { url: Api.AppStore, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: APP应用上下架操作
 */
export const apiChangeAppStoreStatus = (
  state: 'ON_SHELF' | 'OFF_SHELF' | 'NOT_RELEASED',
  ids: number[],
): Promise<BooleanResult> => {
  return defHttp.put(
    { url: Api.AppStoreChange.replace('{state}', state), data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除APP应用
 */
export const apiDeleteAppStore = (ids: number[]): Promise<BooleanResult> => {
  return defHttp.delete(
    { url: Api.AppStoreDelete, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 导出APP应用数据
 */
export const apiExportAppStore = (params?: AppStorePageParams) => {
  return defHttp.get(
    { url: Api.AppStoreExport, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 获取APP应用详情（预留接口）
 */
export const apiGetAppStoreDetail = (id: string | number): Promise<DmsAppStore> => {
  return defHttp.get({ url: Api.AppStoreDetail.replace('{appId}', String(id)) });
};

/**
 * @description: 获取APP应用版本分页列表
 */
export const apiGetAppVersionPage = ({ appId, ...params }): Promise<AppStorePageResult> => {
  return defHttp.get({ url: Api.AppVersionPageInfo.replace('{appId}', String(appId)), params });
};
