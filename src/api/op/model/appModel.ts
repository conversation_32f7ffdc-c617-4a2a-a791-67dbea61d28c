/**
 * APP分类数据模型
 */
export interface DmsAppType {
  id?: number;
  name: string;
  pid?: number;
  level?: number;
  sort?: number;
  disabled?: boolean;
  revision?: number;
  children?: DmsAppType[];
}

/**
 * APP分类树形结构数据模型
 */
export interface TreeVO {
  id: number;
  name: string;
  pid?: number;
  level?: number;
  children?: TreeVO[] | null;
}

/**
 * APP分类分页查询参数
 */
export interface AppTypePageParams {
  current?: number;
  size?: number;
  name?: string;
}

/**
 * 分页响应数据模型
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * APP分类分页响应数据模型
 */
export type AppTypePageResult = DmsAppType;

/**
 * APP分类树形响应数据模型
 */
export type AppTypeTreeResult = TreeVO[];

/**
 * 布尔响应数据模型
 */
export type BooleanResult = boolean;

/**
 * 设备APP数据模型
 */
export interface DmsDeviceApp {
  icon: string;
  name: string;
  packageName?: string;
  appVersion: string;
  versionCode: number;
  runState?: 'RUNNING' | 'NOT_RUNNING' | 'SILENT';
  launcher?: boolean;
  size?: string;
  installDate?: string;
  source?: 'SYSTEM' | 'THIRD' | 'ONESELF';
  newVersion?: string;
  appStore?: DmsAppStore;
}

/**
 * 应用商店数据模型
 */
export interface DmsAppStore {
  id?: number;
  typeId: number;
  name: string;
  size: string;
  introduction?: string;
  platform: string;
  logo: string;
  apk: string;
  packageName: string;
  appVersion: string;
  versionCode: number;
  developer?: string;
  remark?: string;
  revision?: number;
  productIds: string;
  resourcesList?: DmsFileResources[];
  productList?: DmsProduct[];
  typeDesc?: string;
  statusDesc?: string;
}

/**
 * 文件资源数据模型
 */
export interface DmsFileResources {
  id?: number;
  url?: string;
}

/**
 * 产品数据模型
 */
export interface DmsProduct {
  id: number;
  typeId: number;
  model: string;
  sourceCode: 'SELF' | 'THIRD';
  system: 'ANDROID' | 'WINDOWS' | 'LINUX' | 'OTHER';
  screensCount: number;
  mainScreen: string;
  secondScreen: string;
  mainScreenSize: number;
  secondScreenSize: number;
  remark?: string;
  revision?: number;
  state?: boolean;
  typeName?: string;
  sourceName?: string;
  systemName?: string;
  totalCount?: number;
  stockCount?: number;
  customerCount?: number;
  fileResources?: DmsFileResources[];
  image?: string;
}

/**
 * 设备APP分页查询参数
 */
export interface DeviceAppPageParams {
  current?: number;
  size?: number;
  deviceId: number;
  name?: string;
  runState?: 'RUNNING' | 'NOT_RUNNING' | 'SILENT';
  sources?: ('SYSTEM' | 'THIRD' | 'ONESELF')[];
  productId: number;
}

/**
 * 应用商店分页查询参数
 */
export interface AppStorePageParams {
  current?: number;
  size?: number;
  name?: string;
  status?: 'ON_SHELF' | 'OFF_SHELF' | 'NOT_RELEASED';
  typeId?: number;
}

/**
 * 应用商店导出数据模型
 */
export interface DmsAppStoreExcelVO {
  name?: string;
  productNames?: string;
  typeDesc?: string;
  statusDesc?: string;
  updateTime?: string;
}

/**
 * APP操作类型
 */
export type AppOperationType =
  | 'APP_SELECT'
  | 'APP_INSERT'
  | 'APP_UPDATE'
  | 'APP_CLOSE'
  | 'APP_CLEANING'
  | 'APP_UNINSTALL'
  | 'APP_LAUNCHER';

/**
 * 设备APP分页响应数据模型
 */
export type DeviceAppPageResult = PageResult<DmsDeviceApp>;

/**
 * 应用商店分页响应数据模型
 */
export type AppStorePageResult = PageResult<DmsAppStore>;
