/**
 * 固件管理相关数据模型
 */

/**
 * 基础VO数据模型
 */
export interface BaseVO {
  name?: string;
  code?: string;
  total?: number;
}

/**
 * 文件资源数据模型
 */
export interface DmsFileResources {
  id?: number;
  url?: string;
}

/**
 * 产品数据模型
 */
export interface DmsProduct {
  id: number;
  typeId: number;
  model: string;
  sourceCode: 'SELF' | 'THIRD';
  system: 'ANDROID' | 'WINDOWS' | 'LINUX' | 'OTHER';
  screensCount: number;
  mainScreen: string;
  secondScreen: string;
  mainScreenSize: number;
  secondScreenSize: number;
  remark?: string;
  revision?: number;
  state?: boolean;
  typeName?: string;
  sourceName?: string;
  systemName?: string;
  totalCount?: number;
  stockCount?: number;
  customerCount?: number;
  fileResources?: DmsFileResources[];
  image?: string;
}

/**
 * 固件存储数据模型
 */
export interface DmsFirmwareStore {
  id?: number;
  productIds: string;
  name: string;
  version: string;
  signType?: 'MD5';
  sign?: string;
  file: string;
  remark?: string;
  revision?: number;
  productList?: DmsProduct[];
}

/**
 * 固件升级任务数据模型
 */
export interface DmsUpdateTask {
  id?: number;
  firmwareId: number;
  name: string;
  updateType: 'BY_MODEL' | 'BY_PARTNER' | 'BY_CUSTOMER';
  selectModel: 'ALL_MODEL' | 'SELECT_MODEL';
  remark?: string;
  sn?: string;
  count?: number;
  upgradeStatistics?: BaseVO[];
  productIds?: number[];
}

/**
 * 设备固件升级记录数据模型
 */
export interface DmsUpdateRecord {
  id?: number;
  firmwareId?: number;
  taskId?: number;
  deviceId?: number;
  deviceSn?: string;
  status?: 'ALL' | 'INIT' | 'SUCCESS' | 'FAIL';
  dmsUpdateTask?: DmsUpdateTask;
  firmwareStore?: DmsFirmwareStore;
  statusDesc?: string;
}

/**
 * 分页查询参数 - 固件存储
 */
export interface FirmwareStorePageParams {
  current?: number;
  size?: number;
  beginTime?: string;
  endTime?: string;
  name?: string;
  productId?: number;
  model?: string;
}

/**
 * 分页查询参数 - 固件任务
 */
export interface FirmwareTaskPageParams {
  current?: number;
  size?: number;
  beginTime?: string;
  endTime?: string;
  keyword?: string;
  firmwareId?: number;
}

/**
 * 分页查询参数 - 固件升级记录
 */
export interface FirmwareRecordPageParams {
  current?: number;
  size?: number;
  beginTime?: string;
  endTime?: string;
  firmwareId?: number;
  taskId?: number;
  keyword?: string;
  status?: 'ALL' | 'INIT' | 'SUCCESS' | 'FAIL';
  deviceId?: number;
}

/**
 * 分页响应数据模型
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages?: number;
}

/**
 * 固件存储分页响应数据模型
 */
export type FirmwareStorePageResult = PageResult<DmsFirmwareStore>;

/**
 * 固件任务分页响应数据模型
 */
export type FirmwareTaskPageResult = PageResult<DmsUpdateTask>;

/**
 * 固件升级记录分页响应数据模型
 */
export type FirmwareRecordPageResult = PageResult<DmsUpdateRecord>;

/**
 * 布尔响应数据模型
 */
export type BooleanResult = boolean;
