import { defHttp } from '@/utils/http/axios';
import type {
  DmsFirmwareStore,
  DmsUpdateTask,
  FirmwareStorePageParams,
  FirmwareTaskPageParams,
  FirmwareRecordPageParams,
  FirmwareStorePageResult,
  FirmwareTaskPageResult,
  FirmwareRecordPageResult,
  BooleanResult,
} from './model/firmModel';

enum Api {
  // 固件管理
  FirmwareStore = '/admin/sys/firm/store',
  FirmwareStorePageInfo = '/admin/sys/firm/store/pageInfo',
  FirmwareStoreDelete = '/admin/sys/firm/store/{id}',

  // 固件任务管理
  FirmwareTask = '/admin/sys/firm/task',
  FirmwareTaskPageInfo = '/admin/sys/firm/task/pageInfo',

  // 设备固件升级记录
  UpgradeStatistics = '/admin/sys/firm/record/upgradeStatistics/{taskId}',
  FirmwareRecordPageInfo = '/admin/sys/firm/record/pageInfo',
}

// ==================== 固件管理相关接口 ====================

/**
 * @description: 获取固件分页列表
 */
export const apiFirmwareStorePageInfo = (
  params: FirmwareStorePageParams,
): Promise<FirmwareStorePageResult> => {
  return defHttp.get({ url: Api.FirmwareStorePageInfo, params });
};

/**
 * @description: 新增固件
 */
export const apiAddFirmwareStore = (data: DmsFirmwareStore): Promise<BooleanResult> => {
  return defHttp.post(
    { url: Api.FirmwareStore, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改固件
 */
export const apiUpdateFirmwareStore = (data: DmsFirmwareStore): Promise<BooleanResult> => {
  return defHttp.put(
    { url: Api.FirmwareStore, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除固件
 */
export const apiDeleteFirmwareStore = (id: number): Promise<BooleanResult> => {
  return defHttp.delete(
    { url: Api.FirmwareStoreDelete.replace('{id}', id.toString()) },
    {
      successMessageMode: 'message',
    },
  );
};

// ==================== 固件任务管理相关接口 ====================

/**
 * @description: 获取固件任务分页列表
 */
export const apiFirmwareTaskPageInfo = (
  params: FirmwareTaskPageParams,
): Promise<FirmwareTaskPageResult> => {
  return defHttp.get({ url: Api.FirmwareTaskPageInfo, params });
};

/**
 * @description: 新增固件任务
 */
export const apiAddFirmwareTask = (data: DmsUpdateTask): Promise<BooleanResult> => {
  return defHttp.post(
    { url: Api.FirmwareTask, data },
    {
      successMessageMode: 'message',
    },
  );
};

// ==================== 设备固件升级记录相关接口 ====================

/**
 * @description: 获取升级统计
 */
export const apiGetUpgradeStatistics = (taskId: number): Promise<DmsUpdateTask> => {
  return defHttp.get({ url: Api.UpgradeStatistics.replace('{taskId}', taskId.toString()) });
};

/**
 * @description: 获取固件升级记录分页列表
 */
export const apiFirmwareRecordPageInfo = (
  params: FirmwareRecordPageParams,
): Promise<FirmwareRecordPageResult> => {
  return defHttp.get({ url: Api.FirmwareRecordPageInfo, params });
};
