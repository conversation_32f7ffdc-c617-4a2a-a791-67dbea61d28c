import { defHttp } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import type { AxiosRequestConfig } from 'axios';

const { apiUrl } = useGlobSetting();
enum Api {
  TypeTree = '/admin/sys/pr/type/tree',
  Type = '/admin/sys/pr/type',
  InfoPage = '/admin/sys/pr/info/pageInfo',
  Info = '/admin/sys/pr/info',
  InfoDel = '/admin/sys/pr/info/del',
  InfoImport = '/admin/sys/pr/info/import',
  InfoTemplate = '/admin/sys/pr/info/template',
  ExportTemplate = '/admin/sys/pr/info/export',
  InfoList = '/admin/sys/pr/info/list',
  InfoGet = '/admin/sys/pr/info/get/{id}',
}

// 获取采购类型树
export const apiTypeTreeGet = () => defHttp.get({ url: Api.TypeTree });
//新增采购类型
export const apiTypeAdd = (data: any) =>
  defHttp.post(
    { url: Api.Type, data },
    {
      successMessageMode: 'message',
    },
  );

//修改采购类型
export const apiTypeEdit = (data: any) =>
  defHttp.put(
    { url: Api.Type, data },
    {
      successMessageMode: 'message',
    },
  );
//删除采购类型
export const apiTypeDel = (id: any) =>
  defHttp.delete(
    { url: `${Api.Type}/${id}` },
    {
      successMessageMode: 'message',
    },
  );

// 产品信息分页

export const apiInfoPage = (params: any) => defHttp.get({ url: Api.InfoPage, params });

// 添加产品信息
export const apiInfoAdd = (data: any) =>
  defHttp.post(
    {
      url: Api.Info,
      data,
    },
    {
      successMessageMode: 'message',
    },
  );

// 修改产品信息
export const apiInfoEdit = (data: any) =>
  defHttp.put(
    {
      url: Api.Info,
      data,
    },
    {
      successMessageMode: 'message',
    },
  );

// 删除产品信息
export const apiInfoDel = (ids: any[]) =>
  defHttp.delete(
    { url: `${Api.InfoDel}`, data: ids },
    {
      successMessageMode: 'message',
    },
  );

// 导入
export const apiInfoImport = (params: any, config: AxiosRequestConfig) =>
  defHttp.uploadFile({ url: Api.InfoImport, baseURL: apiUrl, ...config }, params);

// 模板下载
export const apiInfoTemplate = () =>
  defHttp.get(
    { url: Api.InfoTemplate, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );

// 导出
export const apiInfoExport = (params: any) =>
  defHttp.get(
    { url: Api.ExportTemplate, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );

// 产品信息列表
export const apiInfoList = (params: any) => defHttp.get({ url: Api.InfoList, params });

// 产品详情

export const apiInfoGet = (id: any) => defHttp.get({ url: Api.InfoGet.replace('{id}', id) });
