import { defHttp } from '@/utils/http/axios';

enum Api {
  InfoPageInfo = '/admin/sys/de/info/pageInfo',
  InfoById = '/admin/sys/de/info/info/{id}', // 根据id获取设备信息
  InfoAdd = '/admin/sys/de/info/add', // 添加设备
  InfoImport = '/admin/sys/de/info/import', // 导入设备
  InfoOperationById = '/admin/sys/de/info/operation/{deviceId}', // 操作设备
  // 导出库存设备
  InfoInventoryExport = '/admin/sys/de/info/inventoryExport',

  // 设备APP管理相关接口
  AppRefresh = '/sys/device/app/refresh/{deviceId}', // 刷新APP列表
  AppOperation = '/sys/device/app/operation/{deviceId}/{appId}', // 操作APP
  AppPageInfo = '/sys/device/app/pageInfo/{index}', // 应用列表[0,1,2]
  AppPageByDevice = '/sys/device/app/pageByDevice/{deviceId}', // 应用列表[3]
}

// 获取设备列表分页
export const apiGetDeviceInfoPage = (params: any) => {
  return defHttp.get({ url: Api.InfoPageInfo, params });
};

// 根据id获取设备信息
export const apiGetDeviceInfoById = (id: string) => {
  return defHttp.get({ url: Api.InfoById.replace('{id}', id) });
};

// 添加设备
export const apiAddDeviceInfo = (data: any) => {
  return defHttp.post(
    { url: Api.InfoAdd, data },
    {
      successMessageMode: 'message',
    },
  );
};

// 导出库存设备
export const apiExportInventoryDevice = (params: any) =>
  defHttp.get(
    { url: Api.InfoInventoryExport, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );

// 刷新APP列表
export const apiRefreshDeviceApp = (deviceId: string) => {
  return defHttp.post(
    { url: Api.AppRefresh.replace('{deviceId}', deviceId) },
    {
      successMessageMode: 'message',
    },
  );
};

// 操作APP
export const apiOperationDeviceApp = (deviceId: string, appId: string, operationType: string) => {
  return defHttp.post(
    {
      url: Api.AppOperation.replace('{deviceId}', deviceId).replace('{appId}', appId),
      params: { operationType },
    },
    {
      successMessageMode: 'message',
    },
  );
};

// 应用列表[0,1,2] - 分页查询设备应用
export const apiGetDeviceAppPageInfo = (index: number, params: any) => {
  return defHttp.get({
    url: Api.AppPageInfo.replace('{index}', index.toString()),
    params,
  });
};

// 应用列表[3] - 根据设备ID分页查询应用
export const apiGetDeviceAppPageByDevice = (deviceId: string, params: any) => {
  return defHttp.get({
    url: Api.AppPageByDevice.replace('{deviceId}', deviceId),
    params,
  });
};
