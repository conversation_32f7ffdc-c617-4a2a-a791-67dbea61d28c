import { CreateMapParams } from '#/utils';
import { tagColorEnum } from '@/enums/colorEnum';

export const SourceCodeList: CreateMapParams[] = [
  ['SELF', ['SELF', '自生产'], tagColorEnum.BLUE],
  ['THIRD', ['THIRD', '第三方'], tagColorEnum.YELLOW],
];

// 操作系统
export const OperationSystemList: CreateMapParams[] = [
  ['WINDOWS', ['WINDOWS', 'Windows'], tagColorEnum.BLUE],
  ['ANDROID', ['ANDROID', 'Android'], tagColorEnum.YELLOW],
  ['LINUX', ['LINUX', 'Linux'], tagColorEnum.GREEN],
  ['OTHER', ['OTHER', 'Other'], tagColorEnum.DEFAULT_BUTTON],
];

export const DeviceStateList: CreateMapParams[] = [
  ['ONLINE', ['ONLINE', '在线'], tagColorEnum.BLUE],
  ['OFFLINE', ['OFFLINE', '离线'], tagColorEnum.YELLOW],
];

// 应用发布平台
export const AppPublishPlatformList: CreateMapParams[] = [
  ['android', ['ANDROID', 'Android'], tagColorEnum.BLUE],
  ['windows', ['WINDOWS', 'Windows'], tagColorEnum.YELLOW],
];

// 应用状态
export const AppStatusList: CreateMapParams[] = [
  ['ON_SHELF', ['ON_SHELF', '已上架'], tagColorEnum.GREEN],
  ['OFF_SHELF', ['OFF_SHELF', '已下架'], tagColorEnum.RED],
  ['NOT_RELEASED', ['NOT_RELEASED', '未发布'], tagColorEnum.YELLOW],
];
