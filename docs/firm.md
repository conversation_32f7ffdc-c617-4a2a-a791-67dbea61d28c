---
title: mds copy
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# mds copy

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 固件管理

<a id="opIdupdate_3"></a>

## PUT 修改固件

PUT /sys/firm/store

修改固件

> Body 请求参数

```json
{
  "id": 0,
  "productIds": "string",
  "name": "string",
  "version": "string",
  "signType": "MD5",
  "sign": "string",
  "file": "string",
  "remark": "string",
  "revision": 0,
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsFirmwareStore](#schemadmsfirmwarestore)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdsave_7"></a>

## POST 新增固件

POST /sys/firm/store

新增固件

> Body 请求参数

```json
{
  "id": 0,
  "productIds": "string",
  "name": "string",
  "version": "string",
  "signType": "MD5",
  "sign": "string",
  "file": "string",
  "remark": "string",
  "revision": 0,
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsFirmwareStore](#schemadmsfirmwarestore)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_3"></a>

## GET 固件列表-分页

GET /sys/firm/store/pageInfo

固件列表-分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int64)| 否 |当前页|
|size|query|integer(int64)| 否 |每页数量|
|beginTime|query|string| 否 |开始时间|
|endTime|query|string| 否 |结束时间|
|name|query|string| 否 |固件名称|
|productId|query|integer(int64)| 否 |产品标识（与产品型号二选一）|
|model|query|string| 否 |产品型号（与产品标识二选一）|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"records":[{"id":0,"productIds":"string","name":"string","version":"string","signType":"MD5","sign":"string","file":"string","remark":"string","revision":0,"productList":[{"id":null,"typeId":null,"model":null,"sourceCode":null,"system":null,"screensCount":null,"mainScreen":null,"secondScreen":null,"mainScreenSize":null,"secondScreenSize":null,"remark":null,"revision":null,"state":null,"typeName":null,"sourceName":null,"systemName":null,"totalCount":null,"stockCount":null,"customerCount":null,"fileResources":null,"image":null}]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{"id":0,"productIds":"string","name":"string","version":"string","signType":"[","sign":"string","file":"string","remark":"string","revision":0,"productList":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"searchCount":{"records":[{"id":0,"productIds":"string","name":"string","version":"string","signType":"[","sign":"string","file":"string","remark":"string","revision":0,"productList":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RPageDmsFirmwareStore](#schemarpagedmsfirmwarestore)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIddelete_1"></a>

## DELETE 删除固件

DELETE /sys/firm/store/{id}

删除固件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 固件任务管理

<a id="opIdsave_19"></a>

## POST 新增固件任务

POST /sys/firm/task

新增固件任务

> Body 请求参数

```json
{
  "id": 0,
  "firmwareId": 0,
  "name": "string",
  "updateType": "BY_MODEL",
  "selectModel": "ALL_MODEL",
  "remark": "string",
  "sn": "string",
  "count": 0,
  "upgradeStatistics": [
    {
      "name": "string",
      "code": "string",
      "total": 0
    }
  ],
  "productIds": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsUpdateTask](#schemadmsupdatetask)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_2"></a>

## GET 固件任务列表-分页

GET /sys/firm/task/pageInfo

固件任务列表-分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int64)| 否 |当前页|
|size|query|integer(int64)| 否 |每页数量|
|beginTime|query|string| 否 |开始时间|
|endTime|query|string| 否 |结束时间|
|keyword|query|string| 否 |关键字|
|firmwareId|query|integer(int64)| 否 |固件标识|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"records":[{"id":0,"firmwareId":0,"name":"string","updateType":"BY_MODEL","selectModel":"ALL_MODEL","remark":"string","sn":"string","count":0,"upgradeStatistics":[{"name":null,"code":null,"total":null}],"productIds":[0]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{"id":0,"firmwareId":0,"name":"string","updateType":"[","selectModel":"[","remark":"string","sn":"string","count":0,"upgradeStatistics":[null],"productIds":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"searchCount":{"records":[{"id":0,"firmwareId":0,"name":"string","updateType":"[","selectModel":"[","remark":"string","sn":"string","count":0,"upgradeStatistics":[null],"productIds":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RPageDmsUpdateTask](#schemarpagedmsupdatetask)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 设备固件升级记录

<a id="opIdupgradeStatistics"></a>

## GET 升级统计

GET /sys/firm/record/upgradeStatistics/{taskId}

升级统计

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|taskId|path|integer(int64)| 是 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"firmwareId":0,"name":"string","updateType":"BY_MODEL","selectModel":"ALL_MODEL","remark":"string","sn":"string","count":0,"upgradeStatistics":[{"name":"string","code":"string","total":0}],"productIds":[0]},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RDmsUpdateTask](#schemardmsupdatetask)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_4"></a>

## GET 固件升级记录列表-分页

GET /sys/firm/record/pageInfo

固件升级记录列表-分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int64)| 否 |当前页|
|size|query|integer(int64)| 否 |每页数量|
|beginTime|query|string| 否 |开始时间|
|endTime|query|string| 否 |结束时间|
|firmwareId|query|integer(int64)| 否 |固件标识|
|taskId|query|integer(int64)| 否 |固件标识|
|keyword|query|string| 否 |关键字|
|status|query|string| 否 |状态|
|deviceId|query|integer(int64)| 否 |设备标识|

#### 枚举值

|属性|值|
|---|---|
|status|ALL|
|status|INIT|
|status|SUCCESS|
|status|FAIL|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"records":[{"id":0,"firmwareId":0,"taskId":0,"deviceId":0,"deviceSn":"string","status":"ALL","dmsUpdateTask":{"id":0,"firmwareId":0,"name":"string","updateType":"[","selectModel":"[","remark":"string","sn":"string","count":0,"upgradeStatistics":[null],"productIds":[null]},"firmwareStore":{"id":0,"productIds":"string","name":"string","version":"string","signType":"[","sign":"string","file":"string","remark":"string","revision":0,"productList":[null]},"statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{"id":0,"firmwareId":0,"taskId":0,"deviceId":0,"deviceSn":"string","status":"[","dmsUpdateTask":{},"firmwareStore":{},"statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"searchCount":{"records":[{"id":0,"firmwareId":0,"taskId":0,"deviceId":0,"deviceSn":"string","status":"[","dmsUpdateTask":{},"firmwareStore":{},"statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RPageDmsUpdateRecord](#schemarpagedmsupdaterecord)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {},
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|object|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true,
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|boolean|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_DmsProduct">DmsProduct</h2>

<a id="schemadmsproduct"></a>
<a id="schema_DmsProduct"></a>
<a id="tocSdmsproduct"></a>
<a id="tocsdmsproduct"></a>

```json
{
  "id": 0,
  "typeId": 0,
  "model": "string",
  "sourceCode": "SELF",
  "system": "ANDROID",
  "screensCount": 0,
  "mainScreen": "string",
  "secondScreen": "string",
  "mainScreenSize": 0,
  "secondScreenSize": 0,
  "remark": "string",
  "revision": 0,
  "state": true,
  "typeName": "string",
  "sourceName": "string",
  "systemName": "string",
  "totalCount": 0,
  "stockCount": 0,
  "customerCount": 0,
  "fileResources": [
    {
      "id": 0,
      "url": "string"
    }
  ],
  "image": "string"
}

```

产品类

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||唯一标识|
|typeId|integer(int64)|true|none||分类标识|
|model|string|true|none||产品型号|
|sourceCode|string|true|none||来源标识code|
|system|string|true|none||操作系统;android,windows,linux,other|
|screensCount|integer(int32)|true|none||屏幕数量|
|mainScreen|string|true|none||主屏分辨率|
|secondScreen|string|true|none||副屏分辨率|
|mainScreenSize|number|true|none||主屏尺寸|
|secondScreenSize|number|true|none||副屏尺寸|
|remark|string|false|none||备注|
|revision|integer(int32)|false|none||乐观锁|
|state|boolean|false|none||状态;0:正常,1:禁用|
|typeName|string|false|none||分类名称[Exp]|
|sourceName|string|false|none||来源名称[Exp]|
|systemName|string|false|none||系统名称[Exp]|
|totalCount|integer(int32)|false|none||总数量[Exp]|
|stockCount|integer(int32)|false|none||库存数量[Exp]|
|customerCount|integer(int32)|false|none||客户数量[Exp]|
|fileResources|[[DmsFileResources](#schemadmsfileresources)]|false|none||文件资源|
|image|string|false|none||产品图片[Exp]|

#### 枚举值

|属性|值|
|---|---|
|sourceCode|SELF|
|sourceCode|THIRD|
|system|ANDROID|
|system|WINDOWS|
|system|LINUX|
|system|OTHER|

<h2 id="tocS_DmsFileResources">DmsFileResources</h2>

<a id="schemadmsfileresources"></a>
<a id="schema_DmsFileResources"></a>
<a id="tocSdmsfileresources"></a>
<a id="tocsdmsfileresources"></a>

```json
{
  "id": 0,
  "url": "string"
}

```

文件资源

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|url|string|false|none||地址|

<h2 id="tocS_DmsFirmwareStore">DmsFirmwareStore</h2>

<a id="schemadmsfirmwarestore"></a>
<a id="schema_DmsFirmwareStore"></a>
<a id="tocSdmsfirmwarestore"></a>
<a id="tocsdmsfirmwarestore"></a>

```json
{
  "id": 0,
  "productIds": "string",
  "name": "string",
  "version": "string",
  "signType": "MD5",
  "sign": "string",
  "file": "string",
  "remark": "string",
  "revision": 0,
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ]
}

```

固件管理

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|productIds|string|true|none||产品标识集合|
|name|string|true|none||固件名称|
|version|string|true|none||固件版本|
|signType|string|false|none||签名方式|
|sign|string|false|none||签名|
|file|string|true|none||文件地址|
|remark|string|false|none||描述|
|revision|integer(int32)|false|none||乐观锁|
|productList|[[DmsProduct](#schemadmsproduct)]|false|none||产品列表[Exp]|

#### 枚举值

|属性|值|
|---|---|
|signType|MD5|

<h2 id="tocS_BaseVO">BaseVO</h2>

<a id="schemabasevo"></a>
<a id="schema_BaseVO"></a>
<a id="tocSbasevo"></a>
<a id="tocsbasevo"></a>

```json
{
  "name": "string",
  "code": "string",
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|false|none||none|
|code|string|false|none||none|
|total|integer(int64)|false|none||none|

<h2 id="tocS_DmsUpdateTask">DmsUpdateTask</h2>

<a id="schemadmsupdatetask"></a>
<a id="schema_DmsUpdateTask"></a>
<a id="tocSdmsupdatetask"></a>
<a id="tocsdmsupdatetask"></a>

```json
{
  "id": 0,
  "firmwareId": 0,
  "name": "string",
  "updateType": "BY_MODEL",
  "selectModel": "ALL_MODEL",
  "remark": "string",
  "sn": "string",
  "count": 0,
  "upgradeStatistics": [
    {
      "name": "string",
      "code": "string",
      "total": 0
    }
  ],
  "productIds": [
    0
  ]
}

```

固件升级任务

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|firmwareId|integer(int64)|true|none||固件标识|
|name|string|true|none||任务名称|
|updateType|string|true|none||筛选升级类型|
|selectModel|string|true|none||升级对应型号|
|remark|string|false|none||任务描述|
|sn|string|false|none||任务编号|
|count|integer(int32)|false|none||设备数量|
|upgradeStatistics|[[BaseVO](#schemabasevo)]|false|none||升级统计|
|productIds|[integer]|false|none||产品标识列表|

#### 枚举值

|属性|值|
|---|---|
|updateType|BY_MODEL|
|updateType|BY_PARTNER|
|updateType|BY_CUSTOMER|
|selectModel|ALL_MODEL|
|selectModel|SELECT_MODEL|

<h2 id="tocS_PageDmsUpdateTask">PageDmsUpdateTask</h2>

<a id="schemapagedmsupdatetask"></a>
<a id="schema_PageDmsUpdateTask"></a>
<a id="tocSpagedmsupdatetask"></a>
<a id="tocspagedmsupdatetask"></a>

```json
{
  "records": [
    {
      "id": 0,
      "firmwareId": 0,
      "name": "string",
      "updateType": "BY_MODEL",
      "selectModel": "ALL_MODEL",
      "remark": "string",
      "sn": "string",
      "count": 0,
      "upgradeStatistics": [
        {
          "name": "string",
          "code": "string",
          "total": 0
        }
      ],
      "productIds": [
        0
      ]
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "name": "string",
        "updateType": "BY_MODEL",
        "selectModel": "ALL_MODEL",
        "remark": "string",
        "sn": "string",
        "count": 0,
        "upgradeStatistics": [
          {
            "name": null,
            "code": null,
            "total": null
          }
        ],
        "productIds": [
          0
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "searchCount": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "name": "string",
        "updateType": "BY_MODEL",
        "selectModel": "ALL_MODEL",
        "remark": "string",
        "sn": "string",
        "count": 0,
        "upgradeStatistics": [
          {
            "name": null,
            "code": null,
            "total": null
          }
        ],
        "productIds": [
          0
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "optimizeJoinOfCountSql": true,
  "maxLimit": 0,
  "countId": "string",
  "pages": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[DmsUpdateTask](#schemadmsupdatetask)]|false|none||[固件升级任务]|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|write-only||none|
|optimizeCountSql|[PageDmsUpdateTask](#schemapagedmsupdatetask)|false|none||none|
|searchCount|[PageDmsUpdateTask](#schemapagedmsupdatetask)|false|none||none|
|optimizeJoinOfCountSql|boolean|false|write-only||none|
|maxLimit|integer(int64)|false|write-only||none|
|countId|string|false|write-only||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RPageDmsUpdateTask">RPageDmsUpdateTask</h2>

<a id="schemarpagedmsupdatetask"></a>
<a id="schema_RPageDmsUpdateTask"></a>
<a id="tocSrpagedmsupdatetask"></a>
<a id="tocsrpagedmsupdatetask"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "name": "string",
        "updateType": "BY_MODEL",
        "selectModel": "ALL_MODEL",
        "remark": "string",
        "sn": "string",
        "count": 0,
        "upgradeStatistics": [
          {
            "name": null,
            "code": null,
            "total": null
          }
        ],
        "productIds": [
          0
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[PageDmsUpdateTask](#schemapagedmsupdatetask)|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_PageDmsFirmwareStore">PageDmsFirmwareStore</h2>

<a id="schemapagedmsfirmwarestore"></a>
<a id="schema_PageDmsFirmwareStore"></a>
<a id="tocSpagedmsfirmwarestore"></a>
<a id="tocspagedmsfirmwarestore"></a>

```json
{
  "records": [
    {
      "id": 0,
      "productIds": "string",
      "name": "string",
      "version": "string",
      "signType": "MD5",
      "sign": "string",
      "file": "string",
      "remark": "string",
      "revision": 0,
      "productList": [
        {
          "id": 0,
          "typeId": 0,
          "model": "string",
          "sourceCode": "SELF",
          "system": "ANDROID",
          "screensCount": 0,
          "mainScreen": "string",
          "secondScreen": "string",
          "mainScreenSize": 0,
          "secondScreenSize": 0,
          "remark": "string",
          "revision": 0,
          "state": true,
          "typeName": "string",
          "sourceName": "string",
          "systemName": "string",
          "totalCount": 0,
          "stockCount": 0,
          "customerCount": 0,
          "fileResources": [
            {}
          ],
          "image": "string"
        }
      ]
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": {
    "records": [
      {
        "id": 0,
        "productIds": "string",
        "name": "string",
        "version": "string",
        "signType": "MD5",
        "sign": "string",
        "file": "string",
        "remark": "string",
        "revision": 0,
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "searchCount": {
    "records": [
      {
        "id": 0,
        "productIds": "string",
        "name": "string",
        "version": "string",
        "signType": "MD5",
        "sign": "string",
        "file": "string",
        "remark": "string",
        "revision": 0,
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "optimizeJoinOfCountSql": true,
  "maxLimit": 0,
  "countId": "string",
  "pages": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[DmsFirmwareStore](#schemadmsfirmwarestore)]|false|none||[固件管理]|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|write-only||none|
|optimizeCountSql|[PageDmsFirmwareStore](#schemapagedmsfirmwarestore)|false|none||none|
|searchCount|[PageDmsFirmwareStore](#schemapagedmsfirmwarestore)|false|none||none|
|optimizeJoinOfCountSql|boolean|false|write-only||none|
|maxLimit|integer(int64)|false|write-only||none|
|countId|string|false|write-only||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RPageDmsFirmwareStore">RPageDmsFirmwareStore</h2>

<a id="schemarpagedmsfirmwarestore"></a>
<a id="schema_RPageDmsFirmwareStore"></a>
<a id="tocSrpagedmsfirmwarestore"></a>
<a id="tocsrpagedmsfirmwarestore"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 0,
        "productIds": "string",
        "name": "string",
        "version": "string",
        "signType": "MD5",
        "sign": "string",
        "file": "string",
        "remark": "string",
        "revision": 0,
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[PageDmsFirmwareStore](#schemapagedmsfirmwarestore)|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_RDmsUpdateTask">RDmsUpdateTask</h2>

<a id="schemardmsupdatetask"></a>
<a id="schema_RDmsUpdateTask"></a>
<a id="tocSrdmsupdatetask"></a>
<a id="tocsrdmsupdatetask"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "firmwareId": 0,
    "name": "string",
    "updateType": "BY_MODEL",
    "selectModel": "ALL_MODEL",
    "remark": "string",
    "sn": "string",
    "count": 0,
    "upgradeStatistics": [
      {
        "name": "string",
        "code": "string",
        "total": 0
      }
    ],
    "productIds": [
      0
    ]
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[DmsUpdateTask](#schemadmsupdatetask)|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_DmsUpdateRecord">DmsUpdateRecord</h2>

<a id="schemadmsupdaterecord"></a>
<a id="schema_DmsUpdateRecord"></a>
<a id="tocSdmsupdaterecord"></a>
<a id="tocsdmsupdaterecord"></a>

```json
{
  "id": 0,
  "firmwareId": 0,
  "taskId": 0,
  "deviceId": 0,
  "deviceSn": "string",
  "status": "ALL",
  "dmsUpdateTask": {
    "id": 0,
    "firmwareId": 0,
    "name": "string",
    "updateType": "BY_MODEL",
    "selectModel": "ALL_MODEL",
    "remark": "string",
    "sn": "string",
    "count": 0,
    "upgradeStatistics": [
      {
        "name": "string",
        "code": "string",
        "total": 0
      }
    ],
    "productIds": [
      0
    ]
  },
  "firmwareStore": {
    "id": 0,
    "productIds": "string",
    "name": "string",
    "version": "string",
    "signType": "MD5",
    "sign": "string",
    "file": "string",
    "remark": "string",
    "revision": 0,
    "productList": [
      {
        "id": 0,
        "typeId": 0,
        "model": "string",
        "sourceCode": "SELF",
        "system": "ANDROID",
        "screensCount": 0,
        "mainScreen": "string",
        "secondScreen": "string",
        "mainScreenSize": 0,
        "secondScreenSize": 0,
        "remark": "string",
        "revision": 0,
        "state": true,
        "typeName": "string",
        "sourceName": "string",
        "systemName": "string",
        "totalCount": 0,
        "stockCount": 0,
        "customerCount": 0,
        "fileResources": [
          {
            "id": null,
            "url": null
          }
        ],
        "image": "string"
      }
    ]
  },
  "statusDesc": "string"
}

```

固件升级记录

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|firmwareId|integer(int64)|false|none||固件标识|
|taskId|integer(int64)|false|none||任务标识|
|deviceId|integer(int64)|false|none||设备标识|
|deviceSn|string|false|none||设备编码|
|status|string|false|none||升级状态;待处理，成功，失败|
|dmsUpdateTask|[DmsUpdateTask](#schemadmsupdatetask)|false|none||升级任务|
|firmwareStore|[DmsFirmwareStore](#schemadmsfirmwarestore)|false|none||固件信息[Exp]|
|statusDesc|string|false|none||升级状态|

#### 枚举值

|属性|值|
|---|---|
|status|ALL|
|status|INIT|
|status|SUCCESS|
|status|FAIL|

<h2 id="tocS_PageDmsUpdateRecord">PageDmsUpdateRecord</h2>

<a id="schemapagedmsupdaterecord"></a>
<a id="schema_PageDmsUpdateRecord"></a>
<a id="tocSpagedmsupdaterecord"></a>
<a id="tocspagedmsupdaterecord"></a>

```json
{
  "records": [
    {
      "id": 0,
      "firmwareId": 0,
      "taskId": 0,
      "deviceId": 0,
      "deviceSn": "string",
      "status": "ALL",
      "dmsUpdateTask": {
        "id": 0,
        "firmwareId": 0,
        "name": "string",
        "updateType": "BY_MODEL",
        "selectModel": "ALL_MODEL",
        "remark": "string",
        "sn": "string",
        "count": 0,
        "upgradeStatistics": [
          {
            "name": null,
            "code": null,
            "total": null
          }
        ],
        "productIds": [
          0
        ]
      },
      "firmwareStore": {
        "id": 0,
        "productIds": "string",
        "name": "string",
        "version": "string",
        "signType": "MD5",
        "sign": "string",
        "file": "string",
        "remark": "string",
        "revision": 0,
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ]
      },
      "statusDesc": "string"
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "taskId": 0,
        "deviceId": 0,
        "deviceSn": "string",
        "status": "ALL",
        "dmsUpdateTask": {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        },
        "firmwareStore": {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        },
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "searchCount": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "taskId": 0,
        "deviceId": 0,
        "deviceSn": "string",
        "status": "ALL",
        "dmsUpdateTask": {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        },
        "firmwareStore": {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        },
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "optimizeJoinOfCountSql": true,
  "maxLimit": 0,
  "countId": "string",
  "pages": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[DmsUpdateRecord](#schemadmsupdaterecord)]|false|none||[固件升级记录]|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|write-only||none|
|optimizeCountSql|[PageDmsUpdateRecord](#schemapagedmsupdaterecord)|false|none||none|
|searchCount|[PageDmsUpdateRecord](#schemapagedmsupdaterecord)|false|none||none|
|optimizeJoinOfCountSql|boolean|false|write-only||none|
|maxLimit|integer(int64)|false|write-only||none|
|countId|string|false|write-only||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RPageDmsUpdateRecord">RPageDmsUpdateRecord</h2>

<a id="schemarpagedmsupdaterecord"></a>
<a id="schema_RPageDmsUpdateRecord"></a>
<a id="tocSrpagedmsupdaterecord"></a>
<a id="tocsrpagedmsupdaterecord"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 0,
        "firmwareId": 0,
        "taskId": 0,
        "deviceId": 0,
        "deviceSn": "string",
        "status": "ALL",
        "dmsUpdateTask": {
          "id": 0,
          "firmwareId": 0,
          "name": "string",
          "updateType": "[",
          "selectModel": "[",
          "remark": "string",
          "sn": "string",
          "count": 0,
          "upgradeStatistics": [
            null
          ],
          "productIds": [
            null
          ]
        },
        "firmwareStore": {
          "id": 0,
          "productIds": "string",
          "name": "string",
          "version": "string",
          "signType": "[",
          "sign": "string",
          "file": "string",
          "remark": "string",
          "revision": 0,
          "productList": [
            null
          ]
        },
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "firmwareId": 0,
          "taskId": 0,
          "deviceId": 0,
          "deviceSn": "string",
          "status": "[",
          "dmsUpdateTask": {},
          "firmwareStore": {},
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[PageDmsUpdateRecord](#schemapagedmsupdaterecord)|false|none||数据|
|ok|boolean|false|read-only||none|

