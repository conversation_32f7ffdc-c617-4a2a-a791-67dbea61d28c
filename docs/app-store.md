---
title: mds copy
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# mds copy

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# APP应用商城

<a id="opIdupdate_6"></a>

## PUT 修改APP

PUT /sys/app/store

修改APP

> Body 请求参数

```json
{
  "id": 0,
  "typeId": 0,
  "name": "string",
  "size": "string",
  "introduction": "string",
  "platform": "string",
  "logo": "string",
  "apk": "string",
  "packageName": "string",
  "appVersion": "string",
  "versionCode": 0,
  "developer": "string",
  "remark": "string",
  "revision": 0,
  "productIds": "string",
  "resourcesList": [
    {
      "id": 0,
      "url": "string"
    }
  ],
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ],
  "typeDesc": "string",
  "statusDesc": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsAppStore](#schemadmsappstore)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdsave_9"></a>

## POST 新增APP

POST /sys/app/store

新增APP

> Body 请求参数

```json
{
  "id": 0,
  "typeId": 0,
  "name": "string",
  "size": "string",
  "introduction": "string",
  "platform": "string",
  "logo": "string",
  "apk": "string",
  "packageName": "string",
  "appVersion": "string",
  "versionCode": 0,
  "developer": "string",
  "remark": "string",
  "revision": 0,
  "productIds": "string",
  "resourcesList": [
    {
      "id": 0,
      "url": "string"
    }
  ],
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ],
  "typeDesc": "string",
  "statusDesc": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsAppStore](#schemadmsappstore)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdchange_2"></a>

## PUT 上下架

PUT /sys/app/store/change/{state}

上下架

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|state|path|string| 是 |none|
|body|body|array[integer]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|state|ON_SHELF|
|state|OFF_SHELF|
|state|NOT_RELEASED|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_13"></a>

## GET APP列表-分页

GET /sys/app/store/pageInfo

APP列表-分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int64)| 否 |当前页|
|size|query|integer(int64)| 否 |每页数量|
|name|query|string| 否 |应用名称|
|status|query|string| 否 |应用状态|
|typeId|query|integer(int64)| 否 |应用分类|

#### 枚举值

|属性|值|
|---|---|
|status|ON_SHELF|
|status|OFF_SHELF|
|status|NOT_RELEASED|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"records":[{"id":0,"typeId":0,"name":"string","size":"string","introduction":"string","platform":"string","logo":"string","apk":"string","packageName":"string","appVersion":"string","versionCode":0,"developer":"string","remark":"string","revision":0,"productIds":"string","resourcesList":[{"id":null,"url":null}],"productList":[{"id":null,"typeId":null,"model":null,"sourceCode":null,"system":null,"screensCount":null,"mainScreen":null,"secondScreen":null,"mainScreenSize":null,"secondScreenSize":null,"remark":null,"revision":null,"state":null,"typeName":null,"sourceName":null,"systemName":null,"totalCount":null,"stockCount":null,"customerCount":null,"fileResources":null,"image":null}],"typeDesc":"string","statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{"id":0,"typeId":0,"name":"string","size":"string","introduction":"string","platform":"string","logo":"string","apk":"string","packageName":"string","appVersion":"string","versionCode":0,"developer":"string","remark":"string","revision":0,"productIds":"string","resourcesList":[null],"productList":[null],"typeDesc":"string","statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"searchCount":{"records":[{"id":0,"typeId":0,"name":"string","size":"string","introduction":"string","platform":"string","logo":"string","apk":"string","packageName":"string","appVersion":"string","versionCode":0,"developer":"string","remark":"string","revision":0,"productIds":"string","resourcesList":[null],"productList":[null],"typeDesc":"string","statusDesc":"string"}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RPageDmsAppStore](#schemarpagedmsappstore)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdexportProduct_1"></a>

## GET 导出

GET /sys/app/store/export

导出

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|query|string| 否 |应用名称|
|status|query|string| 否 |应用状态|
|typeId|query|integer(int64)| 否 |应用分类|

#### 枚举值

|属性|值|
|---|---|
|status|ON_SHELF|
|status|OFF_SHELF|
|status|NOT_RELEASED|

> 返回示例

> 200 Response

```
[{"name":"string","productNames":"string","typeDesc":"string","statusDesc":"string","updateTime":"string"}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[DmsAppStoreExcelVO](#schemadmsappstoreexcelvo)]|false|none||none|
|» name|string|false|none||none|
|» productNames|string|false|none||none|
|» typeDesc|string|false|none||none|
|» statusDesc|string|false|none||none|
|» updateTime|string|false|none||none|

状态码 **403**

*响应信息主体*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|» msg|string|false|none||返回信息|
|» data|object|false|none||数据|
|» ok|boolean|false|read-only||none|

状态码 **500**

*响应信息主体*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|» msg|string|false|none||返回信息|
|» data|object|false|none||数据|
|» ok|boolean|false|read-only||none|

<a id="opIddel_1"></a>

## DELETE 删除APP

DELETE /sys/app/store/del

删除APP

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|array[integer]| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {},
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|object|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true,
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|boolean|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_DmsProduct">DmsProduct</h2>

<a id="schemadmsproduct"></a>
<a id="schema_DmsProduct"></a>
<a id="tocSdmsproduct"></a>
<a id="tocsdmsproduct"></a>

```json
{
  "id": 0,
  "typeId": 0,
  "model": "string",
  "sourceCode": "SELF",
  "system": "ANDROID",
  "screensCount": 0,
  "mainScreen": "string",
  "secondScreen": "string",
  "mainScreenSize": 0,
  "secondScreenSize": 0,
  "remark": "string",
  "revision": 0,
  "state": true,
  "typeName": "string",
  "sourceName": "string",
  "systemName": "string",
  "totalCount": 0,
  "stockCount": 0,
  "customerCount": 0,
  "fileResources": [
    {
      "id": 0,
      "url": "string"
    }
  ],
  "image": "string"
}

```

产品类

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||唯一标识|
|typeId|integer(int64)|true|none||分类标识|
|model|string|true|none||产品型号|
|sourceCode|string|true|none||来源标识code|
|system|string|true|none||操作系统;android,windows,linux,other|
|screensCount|integer(int32)|true|none||屏幕数量|
|mainScreen|string|true|none||主屏分辨率|
|secondScreen|string|true|none||副屏分辨率|
|mainScreenSize|number|true|none||主屏尺寸|
|secondScreenSize|number|true|none||副屏尺寸|
|remark|string|false|none||备注|
|revision|integer(int32)|false|none||乐观锁|
|state|boolean|false|none||状态;0:正常,1:禁用|
|typeName|string|false|none||分类名称[Exp]|
|sourceName|string|false|none||来源名称[Exp]|
|systemName|string|false|none||系统名称[Exp]|
|totalCount|integer(int32)|false|none||总数量[Exp]|
|stockCount|integer(int32)|false|none||库存数量[Exp]|
|customerCount|integer(int32)|false|none||客户数量[Exp]|
|fileResources|[[DmsFileResources](#schemadmsfileresources)]|false|none||文件资源|
|image|string|false|none||产品图片[Exp]|

#### 枚举值

|属性|值|
|---|---|
|sourceCode|SELF|
|sourceCode|THIRD|
|system|ANDROID|
|system|WINDOWS|
|system|LINUX|
|system|OTHER|

<h2 id="tocS_DmsAppStore">DmsAppStore</h2>

<a id="schemadmsappstore"></a>
<a id="schema_DmsAppStore"></a>
<a id="tocSdmsappstore"></a>
<a id="tocsdmsappstore"></a>

```json
{
  "id": 0,
  "typeId": 0,
  "name": "string",
  "size": "string",
  "introduction": "string",
  "platform": "string",
  "logo": "string",
  "apk": "string",
  "packageName": "string",
  "appVersion": "string",
  "versionCode": 0,
  "developer": "string",
  "remark": "string",
  "revision": 0,
  "productIds": "string",
  "resourcesList": [
    {
      "id": 0,
      "url": "string"
    }
  ],
  "productList": [
    {
      "id": 0,
      "typeId": 0,
      "model": "string",
      "sourceCode": "SELF",
      "system": "ANDROID",
      "screensCount": 0,
      "mainScreen": "string",
      "secondScreen": "string",
      "mainScreenSize": 0,
      "secondScreenSize": 0,
      "remark": "string",
      "revision": 0,
      "state": true,
      "typeName": "string",
      "sourceName": "string",
      "systemName": "string",
      "totalCount": 0,
      "stockCount": 0,
      "customerCount": 0,
      "fileResources": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "image": "string"
    }
  ],
  "typeDesc": "string",
  "statusDesc": "string"
}

```

应用管理

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|typeId|integer(int64)|true|none||分类标识|
|name|string|true|none||应用名称|
|size|string|true|none||大小|
|introduction|string|false|none||应用简介|
|platform|string|true|none||平台;android,windows|
|logo|string|true|none||应用logo|
|apk|string|true|none||应用包|
|packageName|string|true|none||包名|
|appVersion|string|true|none||版本|
|versionCode|integer(int32)|true|none||版本号|
|developer|string|false|none||开发者|
|remark|string|false|none||备注|
|revision|integer(int32)|false|none||乐观锁|
|productIds|string|true|none||产品标识|
|resourcesList|[[DmsFileResources](#schemadmsfileresources)]|false|none||文件资源|
|productList|[[DmsProduct](#schemadmsproduct)]|false|none||产品[Exp]|
|typeDesc|string|false|none||分类描述[Exp]|
|statusDesc|string|false|none||状态描述[Exp]|

<h2 id="tocS_DmsFileResources">DmsFileResources</h2>

<a id="schemadmsfileresources"></a>
<a id="schema_DmsFileResources"></a>
<a id="tocSdmsfileresources"></a>
<a id="tocsdmsfileresources"></a>

```json
{
  "id": 0,
  "url": "string"
}

```

文件资源

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|url|string|false|none||地址|

<h2 id="tocS_PageDmsAppStore">PageDmsAppStore</h2>

<a id="schemapagedmsappstore"></a>
<a id="schema_PageDmsAppStore"></a>
<a id="tocSpagedmsappstore"></a>
<a id="tocspagedmsappstore"></a>

```json
{
  "records": [
    {
      "id": 0,
      "typeId": 0,
      "name": "string",
      "size": "string",
      "introduction": "string",
      "platform": "string",
      "logo": "string",
      "apk": "string",
      "packageName": "string",
      "appVersion": "string",
      "versionCode": 0,
      "developer": "string",
      "remark": "string",
      "revision": 0,
      "productIds": "string",
      "resourcesList": [
        {
          "id": 0,
          "url": "string"
        }
      ],
      "productList": [
        {
          "id": 0,
          "typeId": 0,
          "model": "string",
          "sourceCode": "SELF",
          "system": "ANDROID",
          "screensCount": 0,
          "mainScreen": "string",
          "secondScreen": "string",
          "mainScreenSize": 0,
          "secondScreenSize": 0,
          "remark": "string",
          "revision": 0,
          "state": true,
          "typeName": "string",
          "sourceName": "string",
          "systemName": "string",
          "totalCount": 0,
          "stockCount": 0,
          "customerCount": 0,
          "fileResources": [
            {}
          ],
          "image": "string"
        }
      ],
      "typeDesc": "string",
      "statusDesc": "string"
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": {
    "records": [
      {
        "id": 0,
        "typeId": 0,
        "name": "string",
        "size": "string",
        "introduction": "string",
        "platform": "string",
        "logo": "string",
        "apk": "string",
        "packageName": "string",
        "appVersion": "string",
        "versionCode": 0,
        "developer": "string",
        "remark": "string",
        "revision": 0,
        "productIds": "string",
        "resourcesList": [
          {
            "id": null,
            "url": null
          }
        ],
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ],
        "typeDesc": "string",
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "searchCount": {
    "records": [
      {
        "id": 0,
        "typeId": 0,
        "name": "string",
        "size": "string",
        "introduction": "string",
        "platform": "string",
        "logo": "string",
        "apk": "string",
        "packageName": "string",
        "appVersion": "string",
        "versionCode": 0,
        "developer": "string",
        "remark": "string",
        "revision": 0,
        "productIds": "string",
        "resourcesList": [
          {
            "id": null,
            "url": null
          }
        ],
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ],
        "typeDesc": "string",
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "optimizeJoinOfCountSql": true,
  "maxLimit": 0,
  "countId": "string",
  "pages": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[DmsAppStore](#schemadmsappstore)]|false|none||[应用管理]|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|write-only||none|
|optimizeCountSql|[PageDmsAppStore](#schemapagedmsappstore)|false|none||none|
|searchCount|[PageDmsAppStore](#schemapagedmsappstore)|false|none||none|
|optimizeJoinOfCountSql|boolean|false|write-only||none|
|maxLimit|integer(int64)|false|write-only||none|
|countId|string|false|write-only||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RPageDmsAppStore">RPageDmsAppStore</h2>

<a id="schemarpagedmsappstore"></a>
<a id="schema_RPageDmsAppStore"></a>
<a id="tocSrpagedmsappstore"></a>
<a id="tocsrpagedmsappstore"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 0,
        "typeId": 0,
        "name": "string",
        "size": "string",
        "introduction": "string",
        "platform": "string",
        "logo": "string",
        "apk": "string",
        "packageName": "string",
        "appVersion": "string",
        "versionCode": 0,
        "developer": "string",
        "remark": "string",
        "revision": 0,
        "productIds": "string",
        "resourcesList": [
          {
            "id": null,
            "url": null
          }
        ],
        "productList": [
          {
            "id": null,
            "typeId": null,
            "model": null,
            "sourceCode": null,
            "system": null,
            "screensCount": null,
            "mainScreen": null,
            "secondScreen": null,
            "mainScreenSize": null,
            "secondScreenSize": null,
            "remark": null,
            "revision": null,
            "state": null,
            "typeName": null,
            "sourceName": null,
            "systemName": null,
            "totalCount": null,
            "stockCount": null,
            "customerCount": null,
            "fileResources": null,
            "image": null
          }
        ],
        "typeDesc": "string",
        "statusDesc": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "typeId": 0,
          "name": "string",
          "size": "string",
          "introduction": "string",
          "platform": "string",
          "logo": "string",
          "apk": "string",
          "packageName": "string",
          "appVersion": "string",
          "versionCode": 0,
          "developer": "string",
          "remark": "string",
          "revision": 0,
          "productIds": "string",
          "resourcesList": [
            null
          ],
          "productList": [
            null
          ],
          "typeDesc": "string",
          "statusDesc": "string"
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[PageDmsAppStore](#schemapagedmsappstore)|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_DmsAppStoreExcelVO">DmsAppStoreExcelVO</h2>

<a id="schemadmsappstoreexcelvo"></a>
<a id="schema_DmsAppStoreExcelVO"></a>
<a id="tocSdmsappstoreexcelvo"></a>
<a id="tocsdmsappstoreexcelvo"></a>

```json
{
  "name": "string",
  "productNames": "string",
  "typeDesc": "string",
  "statusDesc": "string",
  "updateTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|false|none||none|
|productNames|string|false|none||none|
|typeDesc|string|false|none||none|
|statusDesc|string|false|none||none|
|updateTime|string|false|none||none|

