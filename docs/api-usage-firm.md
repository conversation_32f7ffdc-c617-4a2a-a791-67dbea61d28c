# 固件管理 API 使用说明

本文档说明如何使用固件管理相关的 API 接口。

## 导入方式

```typescript
import {
  // 固件管理
  apiFirmwareStorePageInfo,
  apiAddFirmwareStore,
  apiUpdateFirmwareStore,
  apiDeleteFirmwareStore,
  
  // 固件任务管理
  apiFirmwareTaskPageInfo,
  apiAddFirmwareTask,
  
  // 设备固件升级记录
  apiGetUpgradeStatistics,
  apiFirmwareRecordPageInfo,
} from '@/api/op/firm';

import type {
  DmsFirmwareStore,
  DmsUpdateTask,
  FirmwareStorePageParams,
  FirmwareTaskPageParams,
  FirmwareRecordPageParams,
} from '@/api/op/model/firmModel';
```

## 固件管理接口

### 1. 获取固件分页列表

```typescript
const params: FirmwareStorePageParams = {
  current: 1,
  size: 10,
  name: '固件名称',
  beginTime: '2024-01-01',
  endTime: '2024-12-31',
  productId: 123,
  model: '产品型号'
};

const result = await apiFirmwareStorePageInfo(params);
console.log('固件列表:', result.records);
console.log('总数:', result.total);
```

### 2. 新增固件

```typescript
const firmwareData: DmsFirmwareStore = {
  productIds: '1,2,3',
  name: '新固件名称',
  version: '1.0.0',
  signType: 'MD5',
  sign: 'abc123def456',
  file: '/path/to/firmware.bin',
  remark: '固件描述信息'
};

const success = await apiAddFirmwareStore(firmwareData);
if (success) {
  console.log('固件添加成功');
}
```

### 3. 修改固件

```typescript
const updateData: DmsFirmwareStore = {
  id: 1,
  productIds: '1,2,3',
  name: '更新后的固件名称',
  version: '1.0.1',
  signType: 'MD5',
  sign: 'new_signature',
  file: '/path/to/updated_firmware.bin',
  remark: '更新后的描述',
  revision: 1
};

const success = await apiUpdateFirmwareStore(updateData);
if (success) {
  console.log('固件更新成功');
}
```

### 4. 删除固件

```typescript
const firmwareId = 1;
const success = await apiDeleteFirmwareStore(firmwareId);
if (success) {
  console.log('固件删除成功');
}
```

## 固件任务管理接口

### 1. 获取固件任务分页列表

```typescript
const params: FirmwareTaskPageParams = {
  current: 1,
  size: 10,
  keyword: '任务关键字',
  firmwareId: 123,
  beginTime: '2024-01-01',
  endTime: '2024-12-31'
};

const result = await apiFirmwareTaskPageInfo(params);
console.log('任务列表:', result.records);
```

### 2. 新增固件任务

```typescript
const taskData: DmsUpdateTask = {
  firmwareId: 123,
  name: '固件升级任务',
  updateType: 'BY_MODEL',
  selectModel: 'ALL_MODEL',
  remark: '任务描述',
  productIds: [1, 2, 3]
};

const success = await apiAddFirmwareTask(taskData);
if (success) {
  console.log('任务创建成功');
}
```

## 设备固件升级记录接口

### 1. 获取升级统计

```typescript
const taskId = 123;
const statistics = await apiGetUpgradeStatistics(taskId);
console.log('升级统计:', statistics.upgradeStatistics);
```

### 2. 获取固件升级记录分页列表

```typescript
const params: FirmwareRecordPageParams = {
  current: 1,
  size: 10,
  firmwareId: 123,
  taskId: 456,
  status: 'SUCCESS',
  keyword: '设备关键字',
  deviceId: 789,
  beginTime: '2024-01-01',
  endTime: '2024-12-31'
};

const result = await apiFirmwareRecordPageInfo(params);
console.log('升级记录:', result.records);
```

## 错误处理

所有 API 接口都会自动处理错误，成功的操作会显示成功消息。如需自定义错误处理，可以使用 try-catch：

```typescript
try {
  const result = await apiFirmwareStorePageInfo(params);
  // 处理成功结果
} catch (error) {
  console.error('API 调用失败:', error);
  // 处理错误
}
```

## 注意事项

1. 所有 API 接口的 URL 都已经包含了 `/admin` 前缀
2. 新增和修改操作成功时会自动显示成功消息
3. 删除操作会自动显示确认消息
4. 分页参数中的 `current` 和 `size` 是可选的，有默认值
5. 时间参数格式为字符串，建议使用 ISO 格式或 YYYY-MM-DD 格式
