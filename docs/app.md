---
title: mds copy
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# mds copy

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# APP类型

<a id="opIdupdate_5"></a>

## PUT 修改APP分类

PUT /sys/app/type

修改APP分类

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "pid": 0,
  "level": 0,
  "sort": 0,
  "disabled": true,
  "revision": 0,
  "children": [
    {
      "id": 0,
      "name": "string",
      "pid": 0,
      "level": 0,
      "sort": 0,
      "disabled": true,
      "revision": 0,
      "children": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsAppType](#schemadmsapptype)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdsave_8"></a>

## POST 新增APP分类

POST /sys/app/type

新增APP分类

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "pid": 0,
  "level": 0,
  "sort": 0,
  "disabled": true,
  "revision": 0,
  "children": [
    {
      "id": 0,
      "name": "string",
      "pid": 0,
      "level": 0,
      "sort": 0,
      "disabled": true,
      "revision": 0,
      "children": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[DmsAppType](#schemadmsapptype)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdtree_1"></a>

## GET APP分类tree

GET /sys/app/type/tree

APP分类tree

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":[{"id":0,"name":"string","pid":0,"level":0,"children":null}],"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RListTreeVO](#schemarlisttreevo)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_12"></a>

## GET APP列表-分页

GET /sys/app/type/pageInfo

APP列表-分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int64)| 否 |当前页|
|size|query|integer(int64)| 否 |每页数量|
|name|query|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"records":[{"id":0,"name":"string","pid":0,"level":0,"sort":0,"disabled":true,"revision":0,"children":[{"id":null,"name":null,"pid":null,"level":null,"sort":null,"disabled":null,"revision":null,"children":null}]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{"id":0,"name":"string","pid":0,"level":0,"sort":0,"disabled":true,"revision":0,"children":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"searchCount":{"records":[{"id":0,"name":"string","pid":0,"level":0,"sort":0,"disabled":true,"revision":0,"children":[null]}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"searchCount":{"records":[{}],"total":0,"size":0,"current":0,"optimizeCountSql":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"searchCount":{"records":null,"total":null,"size":null,"current":null,"orders":null,"optimizeCountSql":null,"searchCount":null,"optimizeJoinOfCountSql":null,"maxLimit":null,"countId":null,"pages":null},"pages":0},"pages":0},"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RPageDmsAppType](#schemarpagedmsapptype)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIddelete_2"></a>

## DELETE 删除APP分类

DELETE /sys/app/type/{id}

删除APP分类

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {},
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|object|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_DmsAppType">DmsAppType</h2>

<a id="schemadmsapptype"></a>
<a id="schema_DmsAppType"></a>
<a id="tocSdmsapptype"></a>
<a id="tocsdmsapptype"></a>

```json
{
  "id": 0,
  "name": "string",
  "pid": 0,
  "level": 0,
  "sort": 0,
  "disabled": true,
  "revision": 0,
  "children": [
    {
      "id": 0,
      "name": "string",
      "pid": 0,
      "level": 0,
      "sort": 0,
      "disabled": true,
      "revision": 0,
      "children": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

APP分类

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|name|string|true|none||分类名称|
|pid|integer(int64)|false|none||父级|
|level|integer(int32)|false|none||层级;二级；0,1|
|sort|integer(int32)|false|none||排序|
|disabled|boolean|false|none||是否失效;0:正常,1:失效|
|revision|integer(int32)|false|none||乐观锁|
|children|[[DmsAppType](#schemadmsapptype)]|false|none||子级|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true,
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|boolean|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_RListTreeVO">RListTreeVO</h2>

<a id="schemarlisttreevo"></a>
<a id="schema_RListTreeVO"></a>
<a id="tocSrlisttreevo"></a>
<a id="tocsrlisttreevo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "name": "string",
      "pid": 0,
      "level": 0,
      "children": null
    }
  ],
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[[TreeVO](#schematreevo)]|false|none||数据|
|ok|boolean|false|read-only||none|

<h2 id="tocS_TreeVO">TreeVO</h2>

<a id="schematreevo"></a>
<a id="schema_TreeVO"></a>
<a id="tocStreevo"></a>
<a id="tocstreevo"></a>

```json
{
  "id": 0,
  "name": "string",
  "pid": 0,
  "level": 0,
  "children": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||数据标识|
|name|string|false|none||名称|
|pid|integer(int64)|false|none||父级|
|level|integer(int32)|false|none||层级;三级；0,1,2|
|children|any|false|none||子级|

<h2 id="tocS_PageDmsAppType">PageDmsAppType</h2>

<a id="schemapagedmsapptype"></a>
<a id="schema_PageDmsAppType"></a>
<a id="tocSpagedmsapptype"></a>
<a id="tocspagedmsapptype"></a>

```json
{
  "records": [
    {
      "id": 0,
      "name": "string",
      "pid": 0,
      "level": 0,
      "sort": 0,
      "disabled": true,
      "revision": 0,
      "children": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            {}
          ]
        }
      ]
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": {
    "records": [
      {
        "id": 0,
        "name": "string",
        "pid": 0,
        "level": 0,
        "sort": 0,
        "disabled": true,
        "revision": 0,
        "children": [
          {
            "id": null,
            "name": null,
            "pid": null,
            "level": null,
            "sort": null,
            "disabled": null,
            "revision": null,
            "children": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "searchCount": {
    "records": [
      {
        "id": 0,
        "name": "string",
        "pid": 0,
        "level": 0,
        "sort": 0,
        "disabled": true,
        "revision": 0,
        "children": [
          {
            "id": null,
            "name": null,
            "pid": null,
            "level": null,
            "sort": null,
            "disabled": null,
            "revision": null,
            "children": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "optimizeJoinOfCountSql": true,
  "maxLimit": 0,
  "countId": "string",
  "pages": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[DmsAppType](#schemadmsapptype)]|false|none||[APP分类]|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|write-only||none|
|optimizeCountSql|[PageDmsAppType](#schemapagedmsapptype)|false|none||none|
|searchCount|[PageDmsAppType](#schemapagedmsapptype)|false|none||none|
|optimizeJoinOfCountSql|boolean|false|write-only||none|
|maxLimit|integer(int64)|false|write-only||none|
|countId|string|false|write-only||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RPageDmsAppType">RPageDmsAppType</h2>

<a id="schemarpagedmsapptype"></a>
<a id="schema_RPageDmsAppType"></a>
<a id="tocSrpagedmsapptype"></a>
<a id="tocsrpagedmsapptype"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": 0,
        "name": "string",
        "pid": 0,
        "level": 0,
        "sort": 0,
        "disabled": true,
        "revision": 0,
        "children": [
          {
            "id": null,
            "name": null,
            "pid": null,
            "level": null,
            "sort": null,
            "disabled": null,
            "revision": null,
            "children": null
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "searchCount": {
      "records": [
        {
          "id": 0,
          "name": "string",
          "pid": 0,
          "level": 0,
          "sort": 0,
          "disabled": true,
          "revision": 0,
          "children": [
            null
          ]
        }
      ],
      "total": 0,
      "size": 0,
      "current": 0,
      "orders": [
        {
          "column": "string",
          "asc": true
        }
      ],
      "optimizeCountSql": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "searchCount": {
        "records": [
          {}
        ],
        "total": 0,
        "size": 0,
        "current": 0,
        "orders": [
          {}
        ],
        "optimizeCountSql": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "searchCount": {
          "records": null,
          "total": null,
          "size": null,
          "current": null,
          "orders": null,
          "optimizeCountSql": null,
          "searchCount": null,
          "optimizeJoinOfCountSql": null,
          "maxLimit": null,
          "countId": null,
          "pages": null
        },
        "optimizeJoinOfCountSql": true,
        "maxLimit": 0,
        "countId": "string",
        "pages": 0
      },
      "optimizeJoinOfCountSql": true,
      "maxLimit": 0,
      "countId": "string",
      "pages": 0
    },
    "optimizeJoinOfCountSql": true,
    "maxLimit": 0,
    "countId": "string",
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[PageDmsAppType](#schemapagedmsapptype)|false|none||数据|
|ok|boolean|false|read-only||none|

